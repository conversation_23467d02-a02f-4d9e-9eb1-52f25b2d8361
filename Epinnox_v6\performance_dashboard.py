"""
Performance Dashboard Module
Performance monitoring dashboard for the Epinnox trading system
"""

try:
    from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                                QLabel, QPushButton, QTextEdit, QProgressBar,
                                QGroupBox, QFrame, QScrollArea, QTableWidget,
                                QTableWidgetItem, QHeaderView)
    from PySide6.QtCore import Qt, QTimer, Signal
    from PySide6.QtGui import QFont, QColor, QPalette
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)

class PerformanceDashboard(QWidget):
    """
    Performance monitoring dashboard for trading system
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent

        # Performance data
        self.trades_data = []
        self.metrics_data = {}

        # Initialize UI
        self.setup_ui()

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_performance)
        self.update_timer.start(10000)  # Update every 10 seconds

    def setup_ui(self):
        """Setup the performance dashboard UI"""
        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("PERFORMANCE DASHBOARD")
        title_label.setFont(QFont("Consolas", 14, QFont.Bold))
        title_label.setStyleSheet("color: #00ff44; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Metrics row
        metrics_layout = QHBoxLayout()

        # Overall Performance
        overall_group = self.create_overall_metrics()
        metrics_layout.addWidget(overall_group)

        # Daily Performance
        daily_group = self.create_daily_metrics()
        metrics_layout.addWidget(daily_group)

        # Risk Metrics
        risk_group = self.create_risk_metrics()
        metrics_layout.addWidget(risk_group)

        layout.addLayout(metrics_layout)

        # Recent trades table
        trades_group = self.create_trades_table()
        layout.addWidget(trades_group)

    def create_overall_metrics(self):
        """Create overall performance metrics"""
        group = QGroupBox("Overall Performance")
        group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        layout = QVBoxLayout(group)

        self.total_pnl_label = QLabel("Total PnL: $0.00")
        self.total_pnl_label.setStyleSheet("color: #00ff44; font-size: 12px; font-weight: bold;")
        layout.addWidget(self.total_pnl_label)

        self.total_trades_label = QLabel("Total Trades: 0")
        self.total_trades_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.total_trades_label)

        self.win_rate_label = QLabel("Win Rate: 0%")
        self.win_rate_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.win_rate_label)

        self.avg_trade_label = QLabel("Avg Trade: $0.00")
        self.avg_trade_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.avg_trade_label)

        return group

    def create_daily_metrics(self):
        """Create daily performance metrics"""
        group = QGroupBox("Daily Performance")
        group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        layout = QVBoxLayout(group)

        self.daily_pnl_label = QLabel("Today's PnL: $0.00")
        self.daily_pnl_label.setStyleSheet("color: #00ff44; font-size: 12px; font-weight: bold;")
        layout.addWidget(self.daily_pnl_label)

        self.daily_trades_label = QLabel("Today's Trades: 0")
        self.daily_trades_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.daily_trades_label)

        self.daily_win_rate_label = QLabel("Today's Win Rate: 0%")
        self.daily_win_rate_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.daily_win_rate_label)

        layout.addStretch()
        return group

    def create_risk_metrics(self):
        """Create risk metrics"""
        group = QGroupBox("Risk Metrics")
        group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        layout = QVBoxLayout(group)

        self.max_drawdown_label = QLabel("Max Drawdown: 0%")
        self.max_drawdown_label.setStyleSheet("color: #ff4444;")
        layout.addWidget(self.max_drawdown_label)

        self.sharpe_ratio_label = QLabel("Sharpe Ratio: 0.00")
        self.sharpe_ratio_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.sharpe_ratio_label)

        self.profit_factor_label = QLabel("Profit Factor: 0.00")
        self.profit_factor_label.setStyleSheet("color: #ddd;")
        layout.addWidget(self.profit_factor_label)

        layout.addStretch()
        return group

    def create_trades_table(self):
        """Create recent trades table"""
        group = QGroupBox("Recent Trades")
        group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        layout = QVBoxLayout(group)

        self.trades_table = QTableWidget()
        self.trades_table.setColumnCount(7)
        self.trades_table.setHorizontalHeaderLabels([
            "Time", "Symbol", "Side", "Size", "Price", "PnL", "Status"
        ])

        # Style the table
        self.trades_table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                color: #ddd;
                border: 1px solid #333;
                gridline-color: #333;
                font-family: Consolas;
                font-size: 10px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #333;
                color: #00ff44;
                padding: 5px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """)

        # Set column widths
        header = self.trades_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(6):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        self.trades_table.setMaximumHeight(200)
        layout.addWidget(self.trades_table)

        return group

    def update_performance(self):
        """Update performance metrics"""
        try:
            # This would normally fetch real data from the trading system
            # For now, just update with placeholder data
            self.update_metrics_display()
            self.update_trades_display()

        except Exception as e:
            logger.error(f"Error updating performance dashboard: {e}")

    def update_metrics_display(self):
        """Update metrics display with current data"""
        # Placeholder implementation - would normally calculate from real trade data
        pass

    def update_trades_display(self):
        """Update trades table with recent trades"""
        # Placeholder implementation - would normally show real trades
        pass

    def add_trade(self, trade_data: Dict[str, Any]):
        """Add a new trade to the display"""
        try:
            row_position = self.trades_table.rowCount()
            self.trades_table.insertRow(row_position)

            # Add trade data to table
            items = [
                trade_data.get('timestamp', ''),
                trade_data.get('symbol', ''),
                trade_data.get('side', ''),
                str(trade_data.get('size', 0)),
                f"${trade_data.get('price', 0):.4f}",
                f"${trade_data.get('pnl', 0):.2f}",
                trade_data.get('status', '')
            ]

            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)

                # Color code PnL
                if col == 5:  # PnL column
                    pnl = trade_data.get('pnl', 0)
                    if pnl > 0:
                        item.setForeground(QColor("#00ff44"))
                    elif pnl < 0:
                        item.setForeground(QColor("#ff4444"))

                self.trades_table.setItem(row_position, col, item)

            # Keep only last 50 trades
            if self.trades_table.rowCount() > 50:
                self.trades_table.removeRow(0)

        except Exception as e:
            logger.error(f"Error adding trade to display: {e}")

    def refresh_dashboard(self):
        """Refresh performance dashboard data - called before showing main window"""
        try:
            logger.info("Performance dashboard refresh requested")
            self.update_performance()
        except Exception as e:
            logger.error(f"Error refreshing performance dashboard: {e}")