#!/usr/bin/env python3
"""
Extended Paper Trading Validator
Runs 7+ days of paper trading validation with live market data
"""

import asyncio
import logging
import json
import yaml
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
from core.unified_llm_manager import UnifiedLLMManager
from core.autonomous_position_manager import AutonomousPositionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/extended_paper_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PaperTradingMetrics:
    """Metrics for paper trading validation"""
    start_time: datetime
    end_time: Optional[datetime] = None
    total_trades: int = 0
    successful_trades: int = 0
    failed_trades: int = 0
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    avg_trade_duration: float = 0.0
    sharpe_ratio: float = 0.0
    max_daily_loss: float = 0.0
    consecutive_losses: int = 0
    max_consecutive_losses: int = 0
    system_uptime: float = 0.0
    error_count: int = 0
    recovery_count: int = 0

@dataclass
class DailyReport:
    """Daily trading report"""
    date: str
    trades_count: int
    pnl: float
    win_rate: float
    max_drawdown: float
    system_uptime: float
    errors: int
    recoveries: int
    notes: str

class ExtendedPaperTradingValidator:
    """Extended paper trading validation system"""
    
    def __init__(self, config_path: str = 'config/phase2_autonomous_config.yaml'):
        self.config_path = config_path
        self.config = None
        self.orchestrator = None
        self.metrics = None
        self.daily_reports: List[DailyReport] = []
        self.validation_start = None
        self.validation_duration_days = 7
        self.is_running = False
        
    async def initialize(self) -> bool:
        """Initialize the paper trading validator"""
        try:
            # Load configuration
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            # Initialize metrics
            self.metrics = PaperTradingMetrics(start_time=datetime.now())
            self.validation_start = datetime.now()
            
            # Initialize orchestrator in paper trading mode
            self.orchestrator = AutonomousTradingOrchestrator(TradingMode.PAPER, self.config)
            
            success = await self.orchestrator.initialize()
            if not success:
                logger.error("Failed to initialize trading orchestrator")
                return False
            
            logger.info("✅ Extended Paper Trading Validator initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize validator: {e}")
            return False
    
    async def start_validation(self, duration_days: int = 7) -> bool:
        """Start extended paper trading validation"""
        try:
            self.validation_duration_days = duration_days
            self.is_running = True
            
            logger.info(f"🚀 Starting {duration_days}-day paper trading validation")
            logger.info(f"📅 Start time: {self.validation_start}")
            logger.info(f"📅 Expected end: {self.validation_start + timedelta(days=duration_days)}")
            
            # Start autonomous trading
            await self.orchestrator.start_autonomous_trading()
            
            # Run validation loop
            await self._validation_loop()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            return False
    
    async def _validation_loop(self):
        """Main validation monitoring loop"""
        last_daily_report = datetime.now().date()
        
        while self.is_running:
            try:
                current_time = datetime.now()
                elapsed_days = (current_time - self.validation_start).days
                
                # Check if validation period is complete
                if elapsed_days >= self.validation_duration_days:
                    logger.info(f"✅ Validation period complete ({self.validation_duration_days} days)")
                    await self.stop_validation()
                    break
                
                # Update metrics
                await self._update_metrics()
                
                # Generate daily report if needed
                if current_time.date() > last_daily_report:
                    await self._generate_daily_report(last_daily_report)
                    last_daily_report = current_time.date()
                
                # Check system health
                await self._check_system_health()
                
                # Wait before next check (every 5 minutes)
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"❌ Error in validation loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    async def _update_metrics(self):
        """Update validation metrics"""
        try:
            if not self.orchestrator.position_manager:
                return
            
            # Get position manager status
            status = self.orchestrator.position_manager.get_status()
            
            # Update trade counts
            self.metrics.total_trades = status.get('total_positions', 0)
            
            # Get performance metrics
            perf_metrics = status.get('performance_metrics', {})
            self.metrics.total_pnl = perf_metrics.get('total_pnl', 0.0)
            self.metrics.win_rate = perf_metrics.get('win_rate', 0.0)
            self.metrics.max_drawdown = perf_metrics.get('max_drawdown', 0.0)
            
            # Calculate successful vs failed trades
            closed_positions = status.get('closed_positions', 0)
            failed_positions = status.get('failed_positions', 0)
            self.metrics.successful_trades = closed_positions - failed_positions
            self.metrics.failed_trades = failed_positions
            
            # Update system uptime
            uptime_seconds = (datetime.now() - self.metrics.start_time).total_seconds()
            self.metrics.system_uptime = uptime_seconds / (24 * 3600)  # Convert to days
            
        except Exception as e:
            logger.error(f"❌ Error updating metrics: {e}")
    
    async def _generate_daily_report(self, report_date):
        """Generate daily trading report"""
        try:
            # Calculate daily metrics
            daily_pnl = 0.0  # Would need to track daily P&L changes
            daily_trades = 0  # Would need to track new trades since last report
            daily_win_rate = self.metrics.win_rate
            daily_drawdown = self.metrics.max_drawdown
            daily_uptime = 24.0  # Assume full uptime unless errors detected
            daily_errors = 0  # Would track errors since last report
            daily_recoveries = 0  # Would track recoveries since last report
            
            report = DailyReport(
                date=report_date.strftime('%Y-%m-%d'),
                trades_count=daily_trades,
                pnl=daily_pnl,
                win_rate=daily_win_rate,
                max_drawdown=daily_drawdown,
                system_uptime=daily_uptime,
                errors=daily_errors,
                recoveries=daily_recoveries,
                notes=f"Day {len(self.daily_reports) + 1} of validation"
            )
            
            self.daily_reports.append(report)
            
            logger.info(f"📊 Daily Report {report.date}:")
            logger.info(f"   Trades: {report.trades_count}")
            logger.info(f"   P&L: ${report.pnl:.2f}")
            logger.info(f"   Win Rate: {report.win_rate:.1%}")
            logger.info(f"   Max Drawdown: {report.max_drawdown:.1%}")
            logger.info(f"   Uptime: {report.system_uptime:.1f}h")
            
            # Save daily report
            await self._save_daily_report(report)
            
        except Exception as e:
            logger.error(f"❌ Error generating daily report: {e}")
    
    async def _check_system_health(self):
        """Check system health and recovery"""
        try:
            if not self.orchestrator:
                return
            
            # Check orchestrator health
            health_status = await self.orchestrator.check_system_health()
            
            if not health_status:
                logger.warning("⚠️ System health check failed - attempting recovery")
                self.metrics.error_count += 1
                
                # Attempt recovery
                recovery_success = await self._attempt_recovery()
                if recovery_success:
                    self.metrics.recovery_count += 1
                    logger.info("✅ System recovery successful")
                else:
                    logger.error("❌ System recovery failed")
            
        except Exception as e:
            logger.error(f"❌ Error checking system health: {e}")
    
    async def _attempt_recovery(self) -> bool:
        """Attempt system recovery"""
        try:
            # Stop current operations
            await self.orchestrator.stop_autonomous_trading()
            
            # Wait a moment
            await asyncio.sleep(10)
            
            # Restart autonomous trading
            await self.orchestrator.start_autonomous_trading()
            
            # Verify recovery
            await asyncio.sleep(5)
            health_status = await self.orchestrator.check_system_health()
            
            return health_status
            
        except Exception as e:
            logger.error(f"❌ Recovery attempt failed: {e}")
            return False
    
    async def stop_validation(self):
        """Stop validation and generate final report"""
        try:
            self.is_running = False
            self.metrics.end_time = datetime.now()
            
            # Stop autonomous trading
            if self.orchestrator:
                await self.orchestrator.stop_autonomous_trading()
            
            # Generate final report
            await self._generate_final_report()
            
            logger.info("✅ Extended paper trading validation completed")
            
        except Exception as e:
            logger.error(f"❌ Error stopping validation: {e}")
    
    async def _save_daily_report(self, report: DailyReport):
        """Save daily report to file"""
        try:
            os.makedirs('logs/daily_reports', exist_ok=True)
            
            report_path = f'logs/daily_reports/daily_report_{report.date}.json'
            with open(report_path, 'w') as f:
                json.dump(asdict(report), f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error saving daily report: {e}")
    
    async def _generate_final_report(self):
        """Generate comprehensive final validation report"""
        try:
            # Calculate final metrics
            total_duration = (self.metrics.end_time - self.metrics.start_time).total_seconds() / (24 * 3600)
            
            final_report = {
                'validation_summary': {
                    'start_time': self.metrics.start_time.isoformat(),
                    'end_time': self.metrics.end_time.isoformat(),
                    'duration_days': total_duration,
                    'target_duration_days': self.validation_duration_days,
                    'completion_rate': min(total_duration / self.validation_duration_days * 100, 100)
                },
                'trading_performance': {
                    'total_trades': self.metrics.total_trades,
                    'successful_trades': self.metrics.successful_trades,
                    'failed_trades': self.metrics.failed_trades,
                    'win_rate': self.metrics.win_rate,
                    'total_pnl': self.metrics.total_pnl,
                    'max_drawdown': self.metrics.max_drawdown,
                    'sharpe_ratio': self.metrics.sharpe_ratio
                },
                'system_reliability': {
                    'uptime_days': self.metrics.system_uptime,
                    'uptime_percentage': (self.metrics.system_uptime / total_duration * 100) if total_duration > 0 else 0,
                    'error_count': self.metrics.error_count,
                    'recovery_count': self.metrics.recovery_count,
                    'recovery_rate': (self.metrics.recovery_count / self.metrics.error_count * 100) if self.metrics.error_count > 0 else 100
                },
                'daily_reports': [asdict(report) for report in self.daily_reports],
                'validation_status': 'COMPLETED' if total_duration >= self.validation_duration_days else 'PARTIAL',
                'ready_for_live_trading': self._assess_live_trading_readiness()
            }
            
            # Save final report
            os.makedirs('logs', exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = f'logs/extended_paper_trading_report_{timestamp}.json'
            
            with open(report_path, 'w') as f:
                json.dump(final_report, f, indent=2)
            
            logger.info(f"📄 Final validation report saved: {report_path}")
            
            # Print summary
            self._print_validation_summary(final_report)
            
        except Exception as e:
            logger.error(f"❌ Error generating final report: {e}")
    
    def _assess_live_trading_readiness(self) -> Dict[str, Any]:
        """Assess readiness for live trading"""
        criteria = {
            'min_trades': 50,
            'min_win_rate': 0.60,
            'max_drawdown': 0.15,
            'min_uptime': 0.95,
            'max_error_rate': 0.05
        }
        
        assessment = {
            'trades_sufficient': self.metrics.total_trades >= criteria['min_trades'],
            'win_rate_acceptable': self.metrics.win_rate >= criteria['min_win_rate'],
            'drawdown_acceptable': self.metrics.max_drawdown <= criteria['max_drawdown'],
            'uptime_sufficient': (self.metrics.system_uptime / self.validation_duration_days) >= criteria['min_uptime'],
            'error_rate_acceptable': (self.metrics.error_count / max(self.metrics.total_trades, 1)) <= criteria['max_error_rate']
        }
        
        assessment['overall_ready'] = all(assessment.values())
        assessment['criteria'] = criteria
        assessment['actual_values'] = {
            'total_trades': self.metrics.total_trades,
            'win_rate': self.metrics.win_rate,
            'max_drawdown': self.metrics.max_drawdown,
            'uptime_rate': self.metrics.system_uptime / self.validation_duration_days,
            'error_rate': self.metrics.error_count / max(self.metrics.total_trades, 1)
        }
        
        return assessment
    
    def _print_validation_summary(self, report: Dict[str, Any]):
        """Print validation summary"""
        print("\n" + "="*60)
        print("📊 EXTENDED PAPER TRADING VALIDATION SUMMARY")
        print("="*60)
        
        summary = report['validation_summary']
        performance = report['trading_performance']
        reliability = report['system_reliability']
        readiness = report['ready_for_live_trading']
        
        print(f"⏱️  Duration: {summary['duration_days']:.1f} days ({summary['completion_rate']:.1f}% of target)")
        print(f"📈 Total Trades: {performance['total_trades']}")
        print(f"🎯 Win Rate: {performance['win_rate']:.1%}")
        print(f"💰 Total P&L: ${performance['total_pnl']:.2f}")
        print(f"📉 Max Drawdown: {performance['max_drawdown']:.1%}")
        print(f"⚡ System Uptime: {reliability['uptime_percentage']:.1f}%")
        print(f"🔧 Recovery Rate: {reliability['recovery_rate']:.1f}%")
        
        print(f"\n🚀 Live Trading Readiness: {'✅ READY' if readiness['overall_ready'] else '❌ NOT READY'}")
        
        if not readiness['overall_ready']:
            print("\n⚠️  Issues to address:")
            for criterion, passed in readiness.items():
                if criterion not in ['overall_ready', 'criteria', 'actual_values'] and not passed:
                    print(f"   - {criterion.replace('_', ' ').title()}")
        
        print("="*60)

async def main():
    """Main function for extended paper trading validation"""
    validator = ExtendedPaperTradingValidator()
    
    try:
        # Initialize validator
        success = await validator.initialize()
        if not success:
            logger.error("❌ Failed to initialize validator")
            return
        
        # Start validation (default 7 days, but can be interrupted)
        await validator.start_validation(duration_days=7)
        
    except KeyboardInterrupt:
        logger.info("⏹️  Validation interrupted by user")
        await validator.stop_validation()
    except Exception as e:
        logger.error(f"❌ Validation error: {e}")
    finally:
        if validator.orchestrator:
            try:
                await validator.orchestrator.stop_autonomous_trading()
            except:
                pass

if __name__ == "__main__":
    asyncio.run(main())
