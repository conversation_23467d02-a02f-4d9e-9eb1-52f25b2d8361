#!/usr/bin/env python3
"""
GUI Update Manager
Centralized management of all GUI updates to ensure thread safety and real-time display
"""

import sys
import os
import time
import threading
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QMetaObject, Qt
    from PyQt5.QtWidgets import QApplication
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

class GUIUpdateManager(QObject):
    """Centralized GUI update manager for thread-safe real-time updates"""
    
    # Signals for different types of updates
    market_data_updated = pyqtSignal(dict)
    system_health_updated = pyqtSignal(dict)
    trading_status_updated = pyqtSignal(dict)
    phase3_data_updated = pyqtSignal(dict)
    safety_metrics_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.update_queue = []
        self.is_processing = False
        self.last_update_times = {}
        self.update_intervals = {
            'market_data': 2.0,      # 2 seconds
            'system_health': 5.0,    # 5 seconds
            'trading_status': 3.0,   # 3 seconds
            'phase3_data': 2.0,      # 2 seconds
            'safety_metrics': 2.0    # 2 seconds
        }
        
        # Initialize timers
        self.setup_update_timers()
        
        # Data sources
        self.data_manager = None
        self.gui_integration = None
        
    def setup_update_timers(self):
        """Setup all update timers"""
        if not PYQT_AVAILABLE:
            return
            
        # Main processing timer
        self.main_timer = QTimer()
        self.main_timer.timeout.connect(self.process_update_queue)
        self.main_timer.setSingleShot(False)
        
        # Market data timer
        self.market_timer = QTimer()
        self.market_timer.timeout.connect(self.update_market_data)
        self.market_timer.setSingleShot(False)
        
        # System health timer
        self.health_timer = QTimer()
        self.health_timer.timeout.connect(self.update_system_health)
        self.health_timer.setSingleShot(False)
        
        # Trading status timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_trading_status)
        self.status_timer.setSingleShot(False)
        
        # Safety metrics timer
        self.safety_timer = QTimer()
        self.safety_timer.timeout.connect(self.update_safety_metrics)
        self.safety_timer.setSingleShot(False)
        
        # Start timers with thread safety
        self.start_timers_safely()
    
    def start_timers_safely(self):
        """Start all timers on main thread"""
        if QApplication.instance() is not None:
            # Use QMetaObject.invokeMethod for thread safety
            QMetaObject.invokeMethod(self.main_timer, "start", Qt.QueuedConnection, 500)
            QMetaObject.invokeMethod(self.market_timer, "start", Qt.QueuedConnection, 2000)
            QMetaObject.invokeMethod(self.health_timer, "start", Qt.QueuedConnection, 5000)
            QMetaObject.invokeMethod(self.status_timer, "start", Qt.QueuedConnection, 3000)
            QMetaObject.invokeMethod(self.safety_timer, "start", Qt.QueuedConnection, 2000)
        else:
            # Fallback direct start
            self.main_timer.start(500)
            self.market_timer.start(2000)
            self.health_timer.start(5000)
            self.status_timer.start(3000)
            self.safety_timer.start(2000)
    
    def set_data_manager(self, data_manager):
        """Set the data manager instance"""
        self.data_manager = data_manager
    
    def set_gui_integration(self, gui_integration):
        """Set the GUI integration instance"""
        self.gui_integration = gui_integration
    
    def queue_update(self, update_type, data, priority=1):
        """Queue an update for processing"""
        update_item = {
            'type': update_type,
            'data': data,
            'timestamp': time.time(),
            'priority': priority
        }
        
        # Add to queue with priority sorting
        self.update_queue.append(update_item)
        self.update_queue.sort(key=lambda x: x['priority'])
    
    def process_update_queue(self):
        """Process queued updates on main thread"""
        if self.is_processing or not self.update_queue:
            return
        
        self.is_processing = True
        
        try:
            # Process up to 5 updates per cycle to prevent blocking
            processed = 0
            while self.update_queue and processed < 5:
                update = self.update_queue.pop(0)
                self.emit_update_signal(update['type'], update['data'])
                processed += 1
                
        except Exception as e:
            print(f"Update queue processing error: {e}")
        finally:
            self.is_processing = False
    
    def emit_update_signal(self, update_type, data):
        """Emit appropriate signal for update type"""
        try:
            if update_type == 'market_data':
                self.market_data_updated.emit(data)
            elif update_type == 'system_health':
                self.system_health_updated.emit(data)
            elif update_type == 'trading_status':
                self.trading_status_updated.emit(data)
            elif update_type == 'phase3_data':
                self.phase3_data_updated.emit(data)
            elif update_type == 'safety_metrics':
                self.safety_metrics_updated.emit(data)
        except Exception as e:
            print(f"Signal emission error for {update_type}: {e}")
    
    def update_market_data(self):
        """Update market data from backend"""
        try:
            if self.should_update('market_data'):
                market_data = self.get_latest_market_data()
                if market_data:
                    self.queue_update('market_data', market_data, priority=1)
                    self.last_update_times['market_data'] = time.time()
        except Exception as e:
            print(f"Market data update error: {e}")
    
    def update_system_health(self):
        """Update system health from backend"""
        try:
            if self.should_update('system_health'):
                health_data = self.get_latest_system_health()
                if health_data:
                    self.queue_update('system_health', health_data, priority=2)
                    self.last_update_times['system_health'] = time.time()
        except Exception as e:
            print(f"System health update error: {e}")
    
    def update_trading_status(self):
        """Update trading status from backend"""
        try:
            if self.should_update('trading_status'):
                status_data = self.get_latest_trading_status()
                if status_data:
                    self.queue_update('trading_status', status_data, priority=1)
                    self.last_update_times['trading_status'] = time.time()
        except Exception as e:
            print(f"Trading status update error: {e}")
    
    def update_safety_metrics(self):
        """Update safety metrics from backend"""
        try:
            if self.should_update('safety_metrics'):
                safety_data = self.get_latest_safety_metrics()
                if safety_data:
                    self.queue_update('safety_metrics', safety_data, priority=1)
                    self.last_update_times['safety_metrics'] = time.time()
        except Exception as e:
            print(f"Safety metrics update error: {e}")
    
    def should_update(self, update_type):
        """Check if update type should be updated based on interval"""
        last_update = self.last_update_times.get(update_type, 0)
        interval = self.update_intervals.get(update_type, 5.0)
        return (time.time() - last_update) >= interval
    
    def get_latest_market_data(self):
        """Get latest market data from backend"""
        try:
            if self.data_manager:
                # Get real market data
                return self.data_manager.get_current_market_data()
            elif self.gui_integration:
                # Get from GUI integration
                return self.gui_integration.get_market_data()
            else:
                # Return mock data for testing
                return {
                    'symbol': 'BTC/USDT:USDT',
                    'price': 0,
                    'atr': 0,
                    'flow': 0,
                    'volume': 0,
                    'timestamp': time.time()
                }
        except Exception as e:
            print(f"Market data retrieval error: {e}")
            return None
    
    def get_latest_system_health(self):
        """Get latest system health from backend"""
        try:
            if self.gui_integration:
                return self.gui_integration.get_system_health()
            else:
                return {
                    'overall': 87.5,
                    'methods': 100.0,
                    'data': 75.0,
                    'trading': 75.0,
                    'timestamp': time.time()
                }
        except Exception as e:
            print(f"System health retrieval error: {e}")
            return None
    
    def get_latest_trading_status(self):
        """Get latest trading status from backend"""
        try:
            if self.gui_integration:
                return self.gui_integration.get_trading_status()
            else:
                return {
                    'mode': 'STOPPED',
                    'autonomous': False,
                    'exposure': 0,
                    'daily_pnl': 0,
                    'timestamp': time.time()
                }
        except Exception as e:
            print(f"Trading status retrieval error: {e}")
            return None
    
    def get_latest_safety_metrics(self):
        """Get latest safety metrics from backend"""
        try:
            if self.gui_integration:
                return self.gui_integration.get_safety_metrics()
            else:
                return {
                    'safety_score': 100,
                    'risk_level': 'LOW',
                    'active_positions': 0,
                    'daily_loss': 0,
                    'timestamp': time.time()
                }
        except Exception as e:
            print(f"Safety metrics retrieval error: {e}")
            return None
    
    def force_update_all(self):
        """Force immediate update of all data"""
        self.last_update_times.clear()
        self.update_market_data()
        self.update_system_health()
        self.update_trading_status()
        self.update_safety_metrics()
    
    def stop_all_timers(self):
        """Stop all update timers"""
        if hasattr(self, 'main_timer'):
            self.main_timer.stop()
        if hasattr(self, 'market_timer'):
            self.market_timer.stop()
        if hasattr(self, 'health_timer'):
            self.health_timer.stop()
        if hasattr(self, 'status_timer'):
            self.status_timer.stop()
        if hasattr(self, 'safety_timer'):
            self.safety_timer.stop()

# Global instance
_gui_update_manager = None

def get_gui_update_manager():
    """Get the global GUI update manager instance"""
    global _gui_update_manager
    if _gui_update_manager is None and PYQT_AVAILABLE:
        _gui_update_manager = GUIUpdateManager()
    return _gui_update_manager

def initialize_gui_update_manager(data_manager=None, gui_integration=None):
    """Initialize the GUI update manager with data sources"""
    manager = get_gui_update_manager()
    if manager:
        if data_manager:
            manager.set_data_manager(data_manager)
        if gui_integration:
            manager.set_gui_integration(gui_integration)
    return manager
