"""
Robust WebSocket Connection Manager for Epinnox Trading System
Handles WebSocket connections with automatic reconnection and error recovery
"""

import asyncio
import websockets
import json
import logging
import time
from typing import Dict, Optional, Callable, Any, List
from datetime import datetime, timedelta
from enum import Enum
import ssl

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """WebSocket connection states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"

class WebSocketManager:
    """
    Robust WebSocket connection manager with automatic reconnection
    and comprehensive error handling
    """
    
    def __init__(self, url: str, name: str = "WebSocket"):
        """
        Initialize WebSocket manager
        
        Args:
            url: WebSocket URL to connect to
            name: Name for logging purposes
        """
        self.url = url
        self.name = name
        self.websocket = None
        self.state = ConnectionState.DISCONNECTED
        
        # Connection settings
        self.ping_interval = 20  # Ping every 20 seconds
        self.ping_timeout = 10   # Timeout after 10 seconds
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5  # Start with 5 seconds
        self.max_reconnect_delay = 300  # Max 5 minutes
        
        # Connection tracking
        self.connection_attempts = 0
        self.last_connection_time = None
        self.last_error = None
        self.total_reconnects = 0
        
        # Message handling
        self.message_handlers = {}
        self.error_handlers = []
        self.connection_handlers = []
        self.disconnection_handlers = []
        
        # Health monitoring
        self.last_ping_time = None
        self.last_pong_time = None
        self.message_count = 0
        self.error_count = 0
        
        # Control flags
        self.should_reconnect = True
        self.is_running = False
        
        logger.info(f"WebSocket manager initialized for {self.name}: {self.url}")
    
    async def connect(self) -> bool:
        """
        Connect to WebSocket with retry logic
        
        Returns:
            bool: True if connected successfully
        """
        if self.state == ConnectionState.CONNECTED:
            logger.debug(f"{self.name}: Already connected")
            return True
        
        self.state = ConnectionState.CONNECTING
        self.connection_attempts += 1
        
        try:
            logger.info(f"{self.name}: Connecting to {self.url} (attempt {self.connection_attempts})")
            
            # Create SSL context for secure connections
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Connect with timeout and SSL
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    self.url,
                    ssl=ssl_context if self.url.startswith('wss://') else None,
                    ping_interval=self.ping_interval,
                    ping_timeout=self.ping_timeout,
                    close_timeout=10
                ),
                timeout=30
            )
            
            self.state = ConnectionState.CONNECTED
            self.last_connection_time = datetime.now()
            self.connection_attempts = 0  # Reset on successful connection
            
            logger.info(f"✅ {self.name}: Connected successfully")
            
            # Notify connection handlers
            for handler in self.connection_handlers:
                try:
                    await handler()
                except Exception as e:
                    logger.error(f"Error in connection handler: {e}")
            
            return True
            
        except asyncio.TimeoutError:
            self.last_error = "Connection timeout"
            logger.error(f"❌ {self.name}: Connection timeout")
        except websockets.exceptions.InvalidURI:
            self.last_error = "Invalid WebSocket URI"
            logger.error(f"❌ {self.name}: Invalid URI: {self.url}")
        except websockets.exceptions.InvalidHandshake:
            self.last_error = "Invalid handshake"
            logger.error(f"❌ {self.name}: Invalid handshake")
        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ {self.name}: Connection error: {e}")
        
        self.state = ConnectionState.ERROR
        self.error_count += 1
        return False
    
    async def disconnect(self):
        """Gracefully disconnect from WebSocket"""
        logger.info(f"{self.name}: Disconnecting...")
        
        self.should_reconnect = False
        self.is_running = False
        
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                logger.warning(f"Error closing WebSocket: {e}")
        
        self.state = ConnectionState.DISCONNECTED
        self.websocket = None
        
        # Notify disconnection handlers
        for handler in self.disconnection_handlers:
            try:
                await handler()
            except Exception as e:
                logger.error(f"Error in disconnection handler: {e}")
        
        logger.info(f"✅ {self.name}: Disconnected")
    
    async def send_message(self, message: Dict) -> bool:
        """
        Send message to WebSocket
        
        Args:
            message: Message to send
            
        Returns:
            bool: True if sent successfully
        """
        if self.state != ConnectionState.CONNECTED or not self.websocket:
            logger.warning(f"{self.name}: Cannot send message - not connected")
            return False
        
        try:
            message_str = json.dumps(message)
            await self.websocket.send(message_str)
            logger.debug(f"{self.name}: Sent message: {message_str[:100]}...")
            return True
            
        except websockets.exceptions.ConnectionClosed:
            logger.warning(f"{self.name}: Connection closed while sending message")
            self.state = ConnectionState.DISCONNECTED
            return False
        except Exception as e:
            logger.error(f"{self.name}: Error sending message: {e}")
            return False
    
    async def listen(self):
        """
        Listen for messages with automatic reconnection
        """
        self.is_running = True
        
        while self.is_running and self.should_reconnect:
            try:
                if self.state != ConnectionState.CONNECTED:
                    success = await self.connect()
                    if not success:
                        await self._handle_reconnection()
                        continue
                
                # Listen for messages
                async for message in self.websocket:
                    try:
                        data = json.loads(message)
                        self.message_count += 1
                        await self._handle_message(data)
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"{self.name}: Invalid JSON received: {e}")
                    except Exception as e:
                        logger.error(f"{self.name}: Error handling message: {e}")
                        
            except websockets.exceptions.ConnectionClosed:
                logger.warning(f"{self.name}: Connection closed")
                self.state = ConnectionState.DISCONNECTED
                if self.should_reconnect:
                    await self._handle_reconnection()
                    
            except websockets.exceptions.InvalidMessage:
                logger.warning(f"{self.name}: Invalid message received")
                
            except Exception as e:
                logger.error(f"{self.name}: Unexpected error in listen loop: {e}")
                self.state = ConnectionState.ERROR
                if self.should_reconnect:
                    await self._handle_reconnection()
        
        logger.info(f"{self.name}: Listen loop ended")
    
    async def _handle_reconnection(self):
        """Handle reconnection logic with exponential backoff"""
        if not self.should_reconnect:
            return
        
        self.state = ConnectionState.RECONNECTING
        self.total_reconnects += 1
        
        # Calculate delay with exponential backoff
        delay = min(self.reconnect_delay * (2 ** min(self.connection_attempts, 5)), self.max_reconnect_delay)
        
        logger.info(f"{self.name}: Reconnecting in {delay} seconds (attempt {self.connection_attempts + 1})")
        
        await asyncio.sleep(delay)
    
    async def _handle_message(self, data: Dict):
        """Handle incoming message"""
        try:
            # Check for specific message types
            message_type = data.get('type', 'unknown')
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](data)
            else:
                # Default handler
                if 'default' in self.message_handlers:
                    await self.message_handlers['default'](data)
                    
        except Exception as e:
            logger.error(f"{self.name}: Error in message handler: {e}")
    
    def add_message_handler(self, message_type: str, handler: Callable):
        """Add message handler for specific message type"""
        self.message_handlers[message_type] = handler
        logger.debug(f"{self.name}: Added handler for message type: {message_type}")
    
    def add_connection_handler(self, handler: Callable):
        """Add handler for connection events"""
        self.connection_handlers.append(handler)
    
    def add_disconnection_handler(self, handler: Callable):
        """Add handler for disconnection events"""
        self.disconnection_handlers.append(handler)
    
    def add_error_handler(self, handler: Callable):
        """Add handler for error events"""
        self.error_handlers.append(handler)
    
    def get_connection_stats(self) -> Dict:
        """Get connection statistics"""
        uptime = None
        if self.last_connection_time:
            uptime = (datetime.now() - self.last_connection_time).total_seconds()
        
        return {
            'state': self.state.value,
            'connection_attempts': self.connection_attempts,
            'total_reconnects': self.total_reconnects,
            'message_count': self.message_count,
            'error_count': self.error_count,
            'uptime_seconds': uptime,
            'last_error': self.last_error,
            'is_healthy': self.is_healthy()
        }
    
    def is_healthy(self) -> bool:
        """Check if connection is healthy"""
        if self.state != ConnectionState.CONNECTED:
            return False
        
        # Check if we've had recent activity
        if self.last_connection_time:
            time_since_connection = datetime.now() - self.last_connection_time
            if time_since_connection > timedelta(hours=1) and self.message_count == 0:
                return False  # No messages in over an hour
        
        return True
    
    async def health_check(self) -> bool:
        """Perform health check with ping"""
        if self.state != ConnectionState.CONNECTED or not self.websocket:
            return False
        
        try:
            self.last_ping_time = datetime.now()
            pong_waiter = await self.websocket.ping()
            await asyncio.wait_for(pong_waiter, timeout=self.ping_timeout)
            self.last_pong_time = datetime.now()
            return True
            
        except asyncio.TimeoutError:
            logger.warning(f"{self.name}: Ping timeout")
            return False
        except Exception as e:
            logger.warning(f"{self.name}: Health check failed: {e}")
            return False
