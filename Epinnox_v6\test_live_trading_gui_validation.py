#!/usr/bin/env python3
"""
Live Trading GUI Validation - Phase 3
Comprehensive validation of all GUI components for $50 live trading deployment
"""

import sys
import os
import time
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gui_launch_and_tabs():
    """Test GUI launch and Phase 3 tab accessibility"""
    print("🔍 Testing GUI Launch and Phase 3 Tabs...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import TradingSystemGUI
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create main window
        main_window = TradingSystemGUI()
        
        # Verify tab structure
        if hasattr(main_window, 'tab_widget'):
            tab_count = main_window.tab_widget.count()
            print(f"   ✅ GUI launched with {tab_count} tabs")
            
            # List all tabs
            tabs = []
            for i in range(tab_count):
                tab_name = main_window.tab_widget.tabText(i)
                tabs.append(tab_name)
                print(f"      Tab {i+1}: {tab_name}")
            
            # Verify Phase 3 tabs
            phase3_tabs = ["Autonomous Trading", "Phase 3 Monitoring"]
            found_tabs = []
            for phase3_tab in phase3_tabs:
                for tab in tabs:
                    if phase3_tab in tab:
                        found_tabs.append(tab)
                        break
            
            if len(found_tabs) >= 2:
                print(f"   ✅ Phase 3 tabs found: {found_tabs}")
                return True, main_window
            else:
                print(f"   ⚠️ Phase 3 tabs missing: Expected {phase3_tabs}, Found {found_tabs}")
                return False, main_window
        else:
            print("   ❌ Tab widget not found")
            return False, None
            
    except Exception as e:
        print(f"   ❌ GUI launch test failed: {e}")
        return False, None

def test_autonomous_trading_tab():
    """Test Autonomous Trading tab functionality"""
    print("🔍 Testing Autonomous Trading Tab...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.autonomous_trading_tab import AutonomousTradingTab
        from gui_integration import TradingSystemGUIIntegration
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create autonomous trading tab
        autonomous_tab = AutonomousTradingTab()
        print("   ✅ Autonomous Trading Tab created")
        
        # Create and connect GUI integration
        gui_integration = TradingSystemGUIIntegration()
        success = gui_integration.initialize_phase3_components()
        
        if success:
            print("   ✅ Phase 3 components initialized")
            autonomous_tab.set_gui_integration(gui_integration)
            print("   ✅ GUI integration connected")
        else:
            print("   ⚠️ Phase 3 components initialization failed")
        
        # Test key controls
        controls = [
            'start_autonomous_btn',
            'stop_autonomous_btn', 
            'trading_mode_combo',
            'emergency_stop_btn'
        ]
        
        for control in controls:
            if hasattr(autonomous_tab, control):
                print(f"   ✅ {control} available")
            else:
                print(f"   ❌ {control} missing")
        
        # Test safety controls widget
        if hasattr(autonomous_tab, 'safety_controls_widget'):
            print("   ✅ Safety controls widget integrated")
        else:
            print("   ⚠️ Safety controls widget not found")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Autonomous Trading Tab test failed: {e}")
        return False

def test_safety_controls_widget():
    """Test Safety Controls Widget functionality"""
    print("🔍 Testing Safety Controls Widget...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.safety_controls_widget import SafetyControlsWidget
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create safety controls widget
        safety_widget = SafetyControlsWidget()
        print("   ✅ Safety Controls Widget created")
        
        # Test ultra-conservative settings
        settings = safety_widget.get_ultra_conservative_settings()
        print("   ✅ Ultra-conservative settings retrieved")
        
        # Verify critical settings for $50 account
        critical_settings = {
            'max_total_exposure': 100.0,
            'max_position_size': 20.0,
            'max_daily_loss': 10.0,
            'max_portfolio_risk': 0.02
        }
        
        for setting, expected in critical_settings.items():
            actual = settings.get(setting, 'N/A')
            if actual == expected:
                print(f"   ✅ {setting}: ${actual} (appropriate for $50 account)")
            else:
                print(f"   ⚠️ {setting}: ${actual} (expected: ${expected})")
        
        # Test emergency controls
        if hasattr(safety_widget, 'emergency_stop_btn'):
            print("   ✅ Emergency stop button available")
        else:
            print("   ❌ Emergency stop button missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Safety Controls Widget test failed: {e}")
        return False

def test_monitoring_dashboard():
    """Test Phase 3 Monitoring Dashboard"""
    print("🔍 Testing Phase 3 Monitoring Dashboard...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.phase3_monitoring_dashboard import Phase3MonitoringDashboard
        from gui_integration import TradingSystemGUIIntegration
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create monitoring dashboard
        dashboard = Phase3MonitoringDashboard()
        print("   ✅ Phase 3 Monitoring Dashboard created")
        
        # Create and connect GUI integration
        gui_integration = TradingSystemGUIIntegration()
        gui_integration.initialize_phase3_components()
        dashboard.set_gui_integration(gui_integration)
        print("   ✅ GUI integration connected")
        
        # Test dashboard components
        components = [
            'validation_results_table',
            'deployment_status_label',
            'safety_score_lcd',
            'trading_mode_label'
        ]
        
        for component in components:
            if hasattr(dashboard, component):
                print(f"   ✅ {component} available")
            else:
                print(f"   ❌ {component} missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Monitoring Dashboard test failed: {e}")
        return False

def test_real_time_data_updates():
    """Test real-time data updates in GUI"""
    print("🔍 Testing Real-time Data Updates...")
    
    try:
        from gui_integration import TradingSystemGUIIntegration
        
        # Create GUI integration
        gui_integration = TradingSystemGUIIntegration()
        gui_integration.initialize_phase3_components()
        
        # Test validation status
        validation_status = gui_integration.get_validation_status()
        print(f"   ✅ Validation status retrieved: {len(validation_status)} items")
        
        # Test safety metrics
        safety_metrics = gui_integration.get_safety_metrics()
        print(f"   ✅ Safety metrics retrieved: {len(safety_metrics)} items")
        
        # Test data cache
        if hasattr(gui_integration, 'data_cache'):
            print("   ✅ Data cache available for real-time updates")
        else:
            print("   ⚠️ Data cache not found")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Real-time data updates test failed: {e}")
        return False

def test_emergency_procedures():
    """Test emergency procedures and safety systems"""
    print("🔍 Testing Emergency Procedures...")
    
    try:
        from gui_integration import TradingSystemGUIIntegration
        
        # Create GUI integration
        gui_integration = TradingSystemGUIIntegration()
        gui_integration.initialize_phase3_components()
        
        # Test emergency stop availability
        if hasattr(gui_integration, 'emergency_stop'):
            print("   ✅ Emergency stop method available")
        else:
            print("   ❌ Emergency stop method missing")
        
        # Test safety validation
        if hasattr(gui_integration, 'run_safety_system_validation'):
            print("   ✅ Safety system validation available")
        else:
            print("   ❌ Safety system validation missing")
        
        # Test validation methods
        validation_methods = [
            'run_paper_trading_validation',
            'run_live_market_data_test',
            'check_deployment_readiness'
        ]
        
        for method in validation_methods:
            if hasattr(gui_integration, method):
                print(f"   ✅ {method} available")
            else:
                print(f"   ❌ {method} missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Emergency procedures test failed: {e}")
        return False

def test_50_dollar_account_configuration():
    """Test configuration appropriate for $50 account"""
    print("🔍 Testing $50 Account Configuration...")
    
    try:
        from gui.safety_controls_widget import SafetyControlsWidget
        from PyQt5.QtWidgets import QApplication
        
        # Ensure QApplication exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create safety widget
        safety_widget = SafetyControlsWidget()
        settings = safety_widget.get_ultra_conservative_settings()
        
        # Calculate appropriate settings for $50 account
        account_balance = 50.0
        
        # Verify settings are appropriate for $50
        checks = []
        
        # Max total exposure should be <= account balance
        max_exposure = settings.get('max_total_exposure', 0)
        if max_exposure <= account_balance:
            checks.append(f"✅ Max exposure ${max_exposure} <= ${account_balance}")
        else:
            checks.append(f"❌ Max exposure ${max_exposure} > ${account_balance}")
        
        # Max position size should be <= 40% of account
        max_position = settings.get('max_position_size', 0)
        max_recommended_position = account_balance * 0.4
        if max_position <= max_recommended_position:
            checks.append(f"✅ Max position ${max_position} <= ${max_recommended_position:.1f} (40% of account)")
        else:
            checks.append(f"❌ Max position ${max_position} > ${max_recommended_position:.1f} (40% of account)")
        
        # Max daily loss should be <= 20% of account
        max_daily_loss = settings.get('max_daily_loss', 0)
        max_recommended_loss = account_balance * 0.2
        if max_daily_loss <= max_recommended_loss:
            checks.append(f"✅ Max daily loss ${max_daily_loss} <= ${max_recommended_loss:.1f} (20% of account)")
        else:
            checks.append(f"❌ Max daily loss ${max_daily_loss} > ${max_recommended_loss:.1f} (20% of account)")
        
        # Portfolio risk should be <= 5%
        portfolio_risk = settings.get('max_portfolio_risk', 0) * 100
        if portfolio_risk <= 5.0:
            checks.append(f"✅ Portfolio risk {portfolio_risk}% <= 5%")
        else:
            checks.append(f"❌ Portfolio risk {portfolio_risk}% > 5%")
        
        for check in checks:
            print(f"   {check}")
        
        # Overall assessment
        passed_checks = sum(1 for check in checks if check.startswith("   ✅"))
        total_checks = len(checks)
        
        if passed_checks == total_checks:
            print(f"   ✅ All {total_checks} safety checks passed for $50 account")
            return True
        else:
            print(f"   ⚠️ {passed_checks}/{total_checks} safety checks passed")
            return False
        
    except Exception as e:
        print(f"   ❌ $50 account configuration test failed: {e}")
        return False

def generate_validation_report(results):
    """Generate comprehensive validation report"""
    print("\n" + "="*70)
    print("📊 LIVE TRADING GUI VALIDATION REPORT")
    print("="*70)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    print()
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print("\n" + "="*70)
    
    if success_rate >= 90:
        print("🎉 GUI VALIDATION SUCCESSFUL - READY FOR LIVE TRADING")
        readiness = "READY"
    elif success_rate >= 75:
        print("⚠️ GUI VALIDATION PARTIAL - REVIEW REQUIRED")
        readiness = "REVIEW_REQUIRED"
    else:
        print("❌ GUI VALIDATION FAILED - NOT READY")
        readiness = "NOT_READY"
    
    print("="*70)
    
    # Save report
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'readiness': readiness,
        'test_results': results
    }
    
    os.makedirs('logs/validation', exist_ok=True)
    report_path = f'logs/validation/live_trading_gui_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    try:
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"📄 Validation report saved: {report_path}")
    except Exception as e:
        print(f"⚠️ Failed to save report: {e}")
    
    return readiness

def main():
    """Main validation function"""
    print("="*70)
    print("🧪 LIVE TRADING GUI VALIDATION - PHASE 3")
    print("="*70)
    print("💰 Account Balance: $50")
    print("🎯 Target: Ultra-Conservative Live Trading")
    print("="*70)
    
    # Define test suite
    tests = [
        ("GUI Launch and Phase 3 Tabs", test_gui_launch_and_tabs),
        ("Autonomous Trading Tab", test_autonomous_trading_tab),
        ("Safety Controls Widget", test_safety_controls_widget),
        ("Phase 3 Monitoring Dashboard", test_monitoring_dashboard),
        ("Real-time Data Updates", test_real_time_data_updates),
        ("Emergency Procedures", test_emergency_procedures),
        ("$50 Account Configuration", test_50_dollar_account_configuration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        
        try:
            if test_name == "GUI Launch and Phase 3 Tabs":
                result, main_window = test_func()
                results[test_name] = result
            else:
                result = test_func()
                results[test_name] = result
                
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} FAILED: {e}")
            results[test_name] = False
    
    # Generate final report
    readiness = generate_validation_report(results)
    
    return readiness

if __name__ == "__main__":
    readiness = main()
    sys.exit(0 if readiness == "READY" else 1)
