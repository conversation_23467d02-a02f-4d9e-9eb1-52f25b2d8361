"""
Workers Module
Background worker threads for data fetching and processing in the Epinnox trading system
"""

try:
    from PySide6.QtCore import QThread, Signal
    from PySide6.QtWidgets import QApplication
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import time
import logging
from typing import Dict, Any, List, Optional
import traceback

logger = logging.getLogger(__name__)

# Global variables for module state (set by init_workers)
exchange = None
demo_mode = False
DEBUG = False
USE_GPU = False
fetch_open_positions = None
fetch_order_book = None
fetch_account_info = None
fetch_ohlcv = None
fetch_trades = None

class BaseWorker(QThread):
    """Base worker class with common functionality"""

    error = Signal(str)

    def __init__(self):
        super().__init__()
        self.is_cancelled = False

    def cancel(self):
        """Cancel the worker"""
        self.is_cancelled = True

    def run(self):
        """Override this method in subclasses"""
        try:
            self.do_work()
        except Exception as e:
            error_msg = f"Worker error: {str(e)}"
            if DEBUG:
                error_msg += f"\n{traceback.format_exc()}"
            self.error.emit(error_msg)

    def do_work(self):
        """Override this method to implement worker logic"""
        pass

class OrderBookWorker(BaseWorker):
    """Worker for fetching order book data"""

    fetched = Signal(dict)

    def __init__(self, symbol):
        super().__init__()
        self.symbol = symbol

    def do_work(self):
        """Fetch order book data"""
        if self.is_cancelled:
            return

        try:
            if fetch_order_book:
                orderbook = fetch_order_book(self.symbol)
                if orderbook and not self.is_cancelled:
                    self.fetched.emit(orderbook)
            else:
                # Fallback with mock data
                mock_orderbook = {
                    'bids': [[100.0, 1.0], [99.9, 2.0], [99.8, 1.5]],
                    'asks': [[100.1, 1.0], [100.2, 2.0], [100.3, 1.5]],
                    'symbol': self.symbol,
                    'timestamp': time.time() * 1000
                }
                self.fetched.emit(mock_orderbook)

        except Exception as e:
            self.error.emit(f"Failed to fetch orderbook for {self.symbol}: {str(e)}")

class ChartDataWorker(BaseWorker):
    """Worker for fetching chart data (OHLCV and trades)"""

    fetched_ohlcv = Signal(list)
    fetched_trades = Signal(list)

    def __init__(self, symbol, timeframe='1m', chart_type='Line', tick_limit=None):
        super().__init__()
        self.symbol = symbol
        self.timeframe = timeframe
        self.chart_type = chart_type
        self.tick_limit = tick_limit

    def do_work(self):
        """Fetch chart data based on chart type"""
        if self.is_cancelled:
            return

        try:
            if self.chart_type == "Tick":
                # Fetch trades data for tick chart
                if fetch_trades:
                    trades = fetch_trades(self.symbol, limit=self.tick_limit or 100)
                    if trades and not self.is_cancelled:
                        self.fetched_trades.emit(trades)
                else:
                    # Mock trades data
                    mock_trades = []
                    base_price = 100.0
                    for i in range(self.tick_limit or 50):
                        price = base_price + (i * 0.01)
                        mock_trades.append({
                            'timestamp': time.time() * 1000 - (i * 1000),
                            'price': price,
                            'amount': 1.0 + (i * 0.1),
                            'side': 'buy' if i % 2 == 0 else 'sell'
                        })
                    self.fetched_trades.emit(mock_trades)
            else:
                # Fetch OHLCV data for line/candlestick charts
                if fetch_ohlcv:
                    ohlcv = fetch_ohlcv(self.symbol, self.timeframe, limit=100)
                    if ohlcv and not self.is_cancelled:
                        self.fetched_ohlcv.emit(ohlcv)
                else:
                    # Mock OHLCV data
                    mock_ohlcv = []
                    base_price = 100.0
                    for i in range(50):
                        timestamp = time.time() * 1000 - (i * 60000)  # 1 minute intervals
                        open_price = base_price + (i * 0.1)
                        close_price = open_price + 0.05
                        high_price = max(open_price, close_price) + 0.02
                        low_price = min(open_price, close_price) - 0.02
                        volume = 100.0 + (i * 10)

                        mock_ohlcv.append([timestamp, open_price, high_price, low_price, close_price, volume])
                    self.fetched_ohlcv.emit(mock_ohlcv)

        except Exception as e:
            self.error.emit(f"Failed to fetch chart data for {self.symbol}: {str(e)}")

class TickersWorker(BaseWorker):
    """Worker for fetching ticker data for multiple symbols"""

    fetched = Signal(dict)

    def __init__(self, symbols):
        super().__init__()
        self.symbols = symbols if isinstance(symbols, list) else [symbols]

    def do_work(self):
        """Fetch ticker data for all symbols"""
        if self.is_cancelled:
            return

        try:
            tickers = {}

            if exchange and hasattr(exchange, 'fetch_tickers'):
                # Try to fetch real ticker data
                try:
                    all_tickers = exchange.fetch_tickers(self.symbols)
                    for symbol in self.symbols:
                        if symbol in all_tickers:
                            tickers[symbol] = all_tickers[symbol]
                except Exception as e:
                    if DEBUG:
                        logger.warning(f"Failed to fetch real tickers: {e}")
                    # Fall back to mock data

            # Generate mock ticker data if real data not available
            if not tickers:
                for symbol in self.symbols:
                    if self.is_cancelled:
                        break

                    base_price = 100.0
                    change = (hash(symbol) % 200 - 100) / 1000  # -0.1 to +0.1

                    tickers[symbol] = {
                        'symbol': symbol,
                        'last': base_price + change,
                        'bid': base_price + change - 0.001,
                        'ask': base_price + change + 0.001,
                        'high': base_price + change + 0.05,
                        'low': base_price + change - 0.05,
                        'volume': 1000.0 + (hash(symbol) % 5000),
                        'change': change,
                        'percentage': (change / base_price) * 100,
                        'timestamp': time.time() * 1000
                    }

            if tickers and not self.is_cancelled:
                self.fetched.emit(tickers)

        except Exception as e:
            self.error.emit(f"Failed to fetch tickers: {str(e)}")

class TradesWorker(BaseWorker):
    """Worker for fetching recent trades data"""

    fetched = Signal(list)

    def __init__(self, symbol, limit=50):
        super().__init__()
        self.symbol = symbol
        self.limit = limit

    def do_work(self):
        """Fetch recent trades data"""
        if self.is_cancelled:
            return

        try:
            if fetch_trades:
                trades = fetch_trades(self.symbol, limit=self.limit)
                if trades and not self.is_cancelled:
                    self.fetched.emit(trades)
            else:
                # Mock trades data
                mock_trades = []
                base_price = 100.0
                for i in range(self.limit):
                    if self.is_cancelled:
                        break

                    price = base_price + ((i % 20 - 10) * 0.01)
                    amount = 1.0 + (i * 0.1)
                    side = 'buy' if i % 2 == 0 else 'sell'

                    mock_trades.append({
                        'id': f"trade_{i}",
                        'timestamp': time.time() * 1000 - (i * 1000),
                        'datetime': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'symbol': self.symbol,
                        'side': side,
                        'amount': amount,
                        'price': price,
                        'cost': amount * price,
                        'fee': None
                    })

                self.fetched.emit(mock_trades)

        except Exception as e:
            self.error.emit(f"Failed to fetch trades for {self.symbol}: {str(e)}")