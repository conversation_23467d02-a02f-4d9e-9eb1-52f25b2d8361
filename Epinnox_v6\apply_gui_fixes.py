#!/usr/bin/env python3
"""
Apply GUI Update Fixes to Running System
This script applies the GUI update fixes to the currently running Epinnox v6 system
"""

import sys
import os
import time
import signal
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def log_fix_message(message, level="INFO"):
    """Log fix application message"""
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    level_icon = "✅" if level == "SUCCESS" else "🔧" if level == "INFO" else "⚠️" if level == "WARNING" else "❌"
    print(f"[{timestamp}] {level_icon} {message}")

def check_running_gui():
    """Check if GUI is currently running"""
    log_fix_message("Checking for running GUI process...")
    
    try:
        import psutil
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('launch_epinnox.py' in str(cmd) for cmd in proc.info['cmdline']):
                    log_fix_message(f"Found running GUI process: PID {proc.info['pid']}", "SUCCESS")
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        log_fix_message("No running GUI process found", "WARNING")
        return None
        
    except Exception as e:
        log_fix_message(f"Error checking GUI process: {e}", "ERROR")
        return None

def validate_fixes_applied():
    """Validate that the fixes have been applied to the code"""
    log_fix_message("Validating applied fixes...")
    
    fixes_validated = []
    
    # Check autonomous trading tab fix
    try:
        with open('gui/autonomous_trading_tab.py', 'r') as f:
            content = f.read()
            if 'QMetaObject.invokeMethod' in content and 'thread-safe timer start' in content:
                fixes_validated.append("Autonomous Trading Tab timer fix")
                log_fix_message("✓ Autonomous Trading Tab timer fix validated", "SUCCESS")
            else:
                log_fix_message("✗ Autonomous Trading Tab timer fix not found", "WARNING")
    except Exception as e:
        log_fix_message(f"Error validating autonomous tab fix: {e}", "ERROR")
    
    # Check monitoring dashboard fix
    try:
        with open('gui/phase3_monitoring_dashboard.py', 'r') as f:
            content = f.read()
            if 'QMetaObject.invokeMethod' in content and 'thread safety' in content:
                fixes_validated.append("Phase 3 Monitoring Dashboard timer fix")
                log_fix_message("✓ Phase 3 Monitoring Dashboard timer fix validated", "SUCCESS")
            else:
                log_fix_message("✗ Phase 3 Monitoring Dashboard timer fix not found", "WARNING")
    except Exception as e:
        log_fix_message(f"Error validating monitoring dashboard fix: {e}", "ERROR")
    
    # Check safety controls fix
    try:
        with open('gui/safety_controls_widget.py', 'r') as f:
            content = f.read()
            if 'QMetaObject.invokeMethod' in content and 'thread safety' in content:
                fixes_validated.append("Safety Controls Widget timer fix")
                log_fix_message("✓ Safety Controls Widget timer fix validated", "SUCCESS")
            else:
                log_fix_message("✗ Safety Controls Widget timer fix not found", "WARNING")
    except Exception as e:
        log_fix_message(f"Error validating safety controls fix: {e}", "ERROR")
    
    # Check GUI update manager
    try:
        if os.path.exists('gui/gui_update_manager.py'):
            fixes_validated.append("GUI Update Manager")
            log_fix_message("✓ GUI Update Manager file exists", "SUCCESS")
        else:
            log_fix_message("✗ GUI Update Manager file not found", "WARNING")
    except Exception as e:
        log_fix_message(f"Error validating GUI update manager: {e}", "ERROR")
    
    # Check GUI integration update
    try:
        with open('gui_integration.py', 'r') as f:
            content = f.read()
            if 'setup_gui_update_manager' in content and 'gui_update_manager' in content:
                fixes_validated.append("GUI Integration update manager integration")
                log_fix_message("✓ GUI Integration update manager integration validated", "SUCCESS")
            else:
                log_fix_message("✗ GUI Integration update manager integration not found", "WARNING")
    except Exception as e:
        log_fix_message(f"Error validating GUI integration fix: {e}", "ERROR")
    
    return fixes_validated

def test_gui_update_functionality():
    """Test if GUI update functionality is working"""
    log_fix_message("Testing GUI update functionality...")
    
    try:
        # Test PyQt5 availability
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer, QMetaObject, Qt
        
        log_fix_message("✓ PyQt5 components available", "SUCCESS")
        
        # Test if QApplication instance exists
        app = QApplication.instance()
        if app is not None:
            log_fix_message("✓ QApplication instance found", "SUCCESS")
            
            # Test timer creation
            test_timer = QTimer()
            test_timer.setSingleShot(True)
            log_fix_message("✓ QTimer creation successful", "SUCCESS")
            
            # Test thread-safe timer start
            try:
                QMetaObject.invokeMethod(test_timer, "start", Qt.QueuedConnection, 100)
                log_fix_message("✓ Thread-safe timer start successful", "SUCCESS")
                return True
            except Exception as e:
                log_fix_message(f"✗ Thread-safe timer start failed: {e}", "WARNING")
                return False
        else:
            log_fix_message("✗ No QApplication instance found", "WARNING")
            return False
            
    except Exception as e:
        log_fix_message(f"GUI update functionality test failed: {e}", "ERROR")
        return False

def generate_fix_report(gui_pid, fixes_validated, functionality_test):
    """Generate comprehensive fix report"""
    log_fix_message("Generating fix application report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'gui_process_running': gui_pid is not None,
        'gui_pid': gui_pid,
        'fixes_applied': len(fixes_validated),
        'fixes_validated': fixes_validated,
        'functionality_test_passed': functionality_test,
        'status': 'SUCCESS' if functionality_test and len(fixes_validated) >= 4 else 'PARTIAL' if len(fixes_validated) > 0 else 'FAILED'
    }
    
    # Save report
    try:
        import json
        os.makedirs('logs', exist_ok=True)
        report_file = f"logs/gui_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        log_fix_message(f"Fix report saved: {report_file}", "SUCCESS")
    except Exception as e:
        log_fix_message(f"Failed to save fix report: {e}", "ERROR")
    
    return report

def main():
    """Main fix application function"""
    print("🔧 GUI UPDATE FIXES APPLICATION")
    print("="*50)
    print(f"⏰ Started: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    # Step 1: Check if GUI is running
    gui_pid = check_running_gui()
    
    # Step 2: Validate fixes have been applied to code
    fixes_validated = validate_fixes_applied()
    
    # Step 3: Test GUI update functionality
    functionality_test = test_gui_update_functionality()
    
    # Step 4: Generate report
    report = generate_fix_report(gui_pid, fixes_validated, functionality_test)
    
    # Step 5: Display summary
    print("\n" + "="*50)
    print("📊 GUI FIX APPLICATION SUMMARY")
    print("="*50)
    
    if gui_pid:
        print(f"✅ GUI Process: Running (PID: {gui_pid})")
    else:
        print("❌ GUI Process: Not detected")
    
    print(f"🔧 Fixes Applied: {len(fixes_validated)}/5")
    for fix in fixes_validated:
        print(f"   ✓ {fix}")
    
    if functionality_test:
        print("✅ Functionality Test: PASSED")
    else:
        print("❌ Functionality Test: FAILED")
    
    print(f"📈 Overall Status: {report['status']}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    print("="*50)
    
    if report['status'] == 'SUCCESS':
        print("✅ All fixes applied successfully!")
        print("🎯 The GUI should now have improved real-time updates")
        print("🔄 Monitor the GUI for improved performance")
        if gui_pid:
            print("⚠️ Consider restarting the GUI to fully activate all fixes")
    elif report['status'] == 'PARTIAL':
        print("⚠️ Some fixes applied, but issues remain")
        print("🔧 Review failed validations and apply missing fixes")
        print("🔄 Restart the GUI to activate applied fixes")
    else:
        print("❌ Fix application failed")
        print("🔧 Review error messages and fix issues")
        print("🔄 Restart the GUI after fixing issues")
    
    print(f"\n⏰ Completed: {datetime.now().strftime('%H:%M:%S')}")
    
    return report

if __name__ == "__main__":
    try:
        report = main()
        sys.exit(0 if report['status'] == 'SUCCESS' else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Fix application interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fix application failed: {e}")
        sys.exit(1)
