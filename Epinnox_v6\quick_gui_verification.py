#!/usr/bin/env python3
"""
Quick GUI Verification for Live Trading
Simple verification of Phase 3 GUI components without QApplication conflicts
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_imports():
    """Verify all Phase 3 components can be imported"""
    print("🔍 Verifying Phase 3 Component Imports...")
    
    results = {}
    
    # Test core GUI integration
    try:
        from gui_integration import TradingSystemGUIIntegration
        results['GUI Integration'] = True
        print("   ✅ GUI Integration imported")
    except Exception as e:
        results['GUI Integration'] = False
        print(f"   ❌ GUI Integration failed: {e}")
    
    # Test autonomous trading tab
    try:
        from gui.autonomous_trading_tab import AutonomousTradingTab
        results['Autonomous Trading Tab'] = True
        print("   ✅ Autonomous Trading Tab imported")
    except Exception as e:
        results['Autonomous Trading Tab'] = False
        print(f"   ❌ Autonomous Trading Tab failed: {e}")
    
    # Test safety controls widget
    try:
        from gui.safety_controls_widget import SafetyControlsWidget
        results['Safety Controls Widget'] = True
        print("   ✅ Safety Controls Widget imported")
    except Exception as e:
        results['Safety Controls Widget'] = False
        print(f"   ❌ Safety Controls Widget failed: {e}")
    
    # Test monitoring dashboard
    try:
        from gui.phase3_monitoring_dashboard import Phase3MonitoringDashboard
        results['Monitoring Dashboard'] = True
        print("   ✅ Monitoring Dashboard imported")
    except Exception as e:
        results['Monitoring Dashboard'] = False
        print(f"   ❌ Monitoring Dashboard failed: {e}")
    
    # Test main window
    try:
        from gui.main_window import TradingSystemGUI
        results['Main Window'] = True
        print("   ✅ Main Window imported")
    except Exception as e:
        results['Main Window'] = False
        print(f"   ❌ Main Window failed: {e}")
    
    return results

def verify_ultra_conservative_settings():
    """Verify ultra-conservative settings for $50 account"""
    print("\n🔍 Verifying Ultra-Conservative Settings for $50 Account...")
    
    try:
        # Import without creating QApplication
        import importlib.util
        
        # Load the safety controls module
        spec = importlib.util.spec_from_file_location(
            "safety_controls_widget", 
            "gui/safety_controls_widget.py"
        )
        safety_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(safety_module)
        
        # Check if PyQt5 is available
        try:
            from PyQt5.QtWidgets import QApplication
            pyqt_available = True
        except ImportError:
            pyqt_available = False
        
        if pyqt_available:
            # Create QApplication if needed
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            # Create safety widget
            safety_widget = safety_module.SafetyControlsWidget()
            settings = safety_widget.get_ultra_conservative_settings()
            
            print("   ✅ Safety widget created successfully")
            
            # Verify settings for $50 account
            account_balance = 50.0
            
            # Check max total exposure
            max_exposure = settings.get('max_total_exposure', 0)
            if max_exposure <= account_balance:
                print(f"   ✅ Max exposure: ${max_exposure} (appropriate for ${account_balance})")
            else:
                print(f"   ⚠️ Max exposure: ${max_exposure} (too high for ${account_balance})")
            
            # Check max position size
            max_position = settings.get('max_position_size', 0)
            recommended_position = account_balance * 0.4  # 40% max
            if max_position <= recommended_position:
                print(f"   ✅ Max position: ${max_position} (safe for ${account_balance})")
            else:
                print(f"   ⚠️ Max position: ${max_position} (consider reducing for ${account_balance})")
            
            # Check max daily loss
            max_daily_loss = settings.get('max_daily_loss', 0)
            recommended_loss = account_balance * 0.2  # 20% max
            if max_daily_loss <= recommended_loss:
                print(f"   ✅ Max daily loss: ${max_daily_loss} (safe for ${account_balance})")
            else:
                print(f"   ⚠️ Max daily loss: ${max_daily_loss} (consider reducing for ${account_balance})")
            
            # Check portfolio risk
            portfolio_risk = settings.get('max_portfolio_risk', 0) * 100
            if portfolio_risk <= 5.0:
                print(f"   ✅ Portfolio risk: {portfolio_risk}% (ultra-conservative)")
            else:
                print(f"   ⚠️ Portfolio risk: {portfolio_risk}% (consider reducing)")
            
            return True
        else:
            print("   ⚠️ PyQt5 not available - cannot test widget creation")
            return False
            
    except Exception as e:
        print(f"   ❌ Settings verification failed: {e}")
        return False

def verify_phase3_integration():
    """Verify Phase 3 integration functionality"""
    print("\n🔍 Verifying Phase 3 Integration Functionality...")
    
    try:
        from gui_integration import TradingSystemGUIIntegration
        
        # Create GUI integration
        gui_integration = TradingSystemGUIIntegration()
        print("   ✅ GUI Integration instance created")
        
        # Test Phase 3 initialization
        success = gui_integration.initialize_phase3_components()
        if success:
            print("   ✅ Phase 3 components initialized successfully")
        else:
            print("   ⚠️ Phase 3 components initialization failed")
        
        # Test validation status
        validation_status = gui_integration.get_validation_status()
        print(f"   ✅ Validation status retrieved: {len(validation_status)} items")
        
        # Test safety metrics
        safety_metrics = gui_integration.get_safety_metrics()
        print(f"   ✅ Safety metrics retrieved: {len(safety_metrics)} items")
        
        # Test method availability
        methods = [
            'start_autonomous_trading',
            'stop_autonomous_trading',
            'emergency_stop',
            'run_paper_trading_validation',
            'run_live_market_data_test',
            'run_safety_system_validation',
            'check_deployment_readiness'
        ]
        
        available_methods = 0
        for method in methods:
            if hasattr(gui_integration, method):
                available_methods += 1
                print(f"   ✅ {method} available")
            else:
                print(f"   ❌ {method} missing")
        
        print(f"   📊 Methods available: {available_methods}/{len(methods)}")
        
        return available_methods >= len(methods) * 0.8  # 80% threshold
        
    except Exception as e:
        print(f"   ❌ Phase 3 integration verification failed: {e}")
        return False

def verify_live_trading_readiness():
    """Verify system readiness for live trading"""
    print("\n🔍 Verifying Live Trading Readiness...")
    
    try:
        # Check if credentials are available
        try:
            from credentials import HTX_API_KEY, HTX_SECRET_KEY
            if HTX_API_KEY and HTX_SECRET_KEY:
                print("   ✅ Trading credentials available")
                credentials_ok = True
            else:
                print("   ❌ Trading credentials missing")
                credentials_ok = False
        except ImportError:
            print("   ❌ Credentials file not found")
            credentials_ok = False
        
        # Check if trading interface is available
        try:
            from trading.real_trading_interface import RealTradingInterface
            print("   ✅ Real trading interface available")
            trading_interface_ok = True
        except ImportError:
            print("   ❌ Real trading interface not available")
            trading_interface_ok = False
        
        # Check if autonomous orchestrator is available
        try:
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            print("   ✅ Autonomous trading orchestrator available")
            orchestrator_ok = True
        except ImportError:
            print("   ❌ Autonomous trading orchestrator not available")
            orchestrator_ok = False
        
        # Check if safety systems are available
        try:
            from safety_system_final_validation import SafetySystemValidator
            print("   ✅ Safety system validator available")
            safety_ok = True
        except ImportError:
            print("   ❌ Safety system validator not available")
            safety_ok = False
        
        # Overall readiness assessment
        components = [credentials_ok, trading_interface_ok, orchestrator_ok, safety_ok]
        ready_components = sum(components)
        total_components = len(components)
        
        readiness_percentage = (ready_components / total_components) * 100
        print(f"   📊 Live trading readiness: {ready_components}/{total_components} ({readiness_percentage:.1f}%)")
        
        return readiness_percentage >= 75.0
        
    except Exception as e:
        print(f"   ❌ Live trading readiness check failed: {e}")
        return False

def main():
    """Main verification function"""
    print("="*70)
    print("🧪 QUICK GUI VERIFICATION FOR LIVE TRADING")
    print("="*70)
    print("💰 Target Account: $50")
    print("🎯 Mode: Ultra-Conservative Live Trading")
    print("="*70)
    
    # Run verification tests
    results = {}
    
    # Test 1: Component imports
    import_results = verify_imports()
    results['Component Imports'] = all(import_results.values())
    
    # Test 2: Ultra-conservative settings
    results['Ultra-Conservative Settings'] = verify_ultra_conservative_settings()
    
    # Test 3: Phase 3 integration
    results['Phase 3 Integration'] = verify_phase3_integration()
    
    # Test 4: Live trading readiness
    results['Live Trading Readiness'] = verify_live_trading_readiness()
    
    # Generate summary
    print("\n" + "="*70)
    print("📊 VERIFICATION SUMMARY")
    print("="*70)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("\n🎉 VERIFICATION SUCCESSFUL - READY FOR LIVE TRADING")
        readiness = "READY"
    elif success_rate >= 75:
        print("\n⚠️ VERIFICATION PARTIAL - REVIEW REQUIRED")
        readiness = "REVIEW_REQUIRED"
    else:
        print("\n❌ VERIFICATION FAILED - NOT READY")
        readiness = "NOT_READY"
    
    print("="*70)
    
    return readiness

if __name__ == "__main__":
    readiness = main()
    print(f"\nFinal Status: {readiness}")
    sys.exit(0 if readiness == "READY" else 1)
