"""
GUI Integration Module for EPINNOX v6 - Phase 3 Enhanced
Integrates trading system with GUI components including autonomous orchestrator and safety systems
"""

import logging
import asyncio
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import threading
import time
import json
import os

logger = logging.getLogger(__name__)

try:
    from PyQt5.QtCore import QObject, pyqtSignal, QTimer
    from PyQt5.QtWidgets import QApplication
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    # Mock classes for when PyQt5 is not available
    class QObject:
        pass

    def pyqtSignal(*args):
        def decorator(func):
            return func
        return decorator

# Import Phase 3 components
try:
    from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
    from extended_paper_trading_validator import ExtendedPaperTradingValidator
    from live_market_data_tester import LiveMarketDataTester
    from ultra_conservative_live_deployment import UltraConservativeLiveDeployment
    from safety_system_final_validation import SafetySystemValidator
    PHASE3_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Phase 3 components not available: {e}")
    PHASE3_AVAILABLE = False

class TradingSystemGUIIntegration(QObject if PYQT_AVAILABLE else object):
    """
    Enhanced integration layer between trading system and GUI - Phase 3
    Handles data flow, event management, and Phase 3 autonomous trading components
    """

    # Signals for GUI updates (only work if PyQt5 is available)
    if PYQT_AVAILABLE:
        data_updated = pyqtSignal(dict)
        trade_executed = pyqtSignal(dict)
        error_occurred = pyqtSignal(str)
        status_changed = pyqtSignal(str)
        # Phase 3 specific signals
        validation_completed = pyqtSignal(dict)
        safety_alert = pyqtSignal(str, str)  # level, message
        deployment_status_changed = pyqtSignal(str)
        autonomous_decision_made = pyqtSignal(dict)

    def __init__(self, trading_system=None):
        if PYQT_AVAILABLE:
            super().__init__()

        self.trading_system = trading_system
        self.gui_components = {}
        self.data_cache = {}
        self.update_callbacks = []
        self.is_running = False
        self.update_thread = None
        self.update_interval = 5.0  # seconds

        # Phase 3 components
        self.autonomous_orchestrator = None
        self.paper_trading_validator = None
        self.market_data_tester = None
        self.deployment_manager = None
        self.safety_validator = None

        # Phase 3 state tracking
        self.validation_status = {}
        self.safety_status = {}
        self.deployment_readiness = False
        self.autonomous_mode_active = False

        # Initialize GUI update manager
        self.gui_update_manager = None
        self.setup_gui_update_manager()

        logger.info("Enhanced GUI Integration (Phase 3) initialized")

    def setup_gui_update_manager(self):
        """Setup the centralized GUI update manager"""
        try:
            from gui.gui_update_manager import get_gui_update_manager, initialize_gui_update_manager

            # Initialize the update manager
            self.gui_update_manager = initialize_gui_update_manager(
                data_manager=getattr(self.trading_system, 'data_manager', None) if self.trading_system else None,
                gui_integration=self
            )

            if self.gui_update_manager:
                logger.info("GUI Update Manager initialized successfully")
            else:
                logger.warning("GUI Update Manager initialization failed")

        except Exception as e:
            logger.error(f"Failed to setup GUI Update Manager: {e}")
            self.gui_update_manager = None

    def register_gui_component(self, name: str, component: Any):
        """Register a GUI component for updates"""
        self.gui_components[name] = component
        logger.info(f"Registered GUI component: {name}")
    
    def unregister_gui_component(self, name: str):
        """Unregister a GUI component"""
        if name in self.gui_components:
            del self.gui_components[name]
            logger.info(f"Unregistered GUI component: {name}")
    
    def add_update_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a callback function for data updates"""
        self.update_callbacks.append(callback)
    
    def remove_update_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Remove a callback function"""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
    
    def start_updates(self, interval: float = 5.0):
        """Start automatic GUI updates"""
        self.update_interval = interval
        self.is_running = True
        
        if not self.update_thread or not self.update_thread.is_alive():
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            logger.info(f"Started GUI updates with {interval}s interval")
    
    def stop_updates(self):
        """Stop automatic GUI updates"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
        logger.info("Stopped GUI updates")

    # Phase 3 Integration Methods
    def initialize_phase3_components(self, config_path: str = 'config/phase2_autonomous_config.yaml'):
        """Initialize Phase 3 autonomous trading and safety components"""
        if not PHASE3_AVAILABLE:
            logger.error("Phase 3 components not available")
            return False

        try:
            # Initialize autonomous orchestrator
            self.autonomous_orchestrator = AutonomousTradingOrchestrator(TradingMode.PAPER, {})

            # Initialize validation components
            self.paper_trading_validator = ExtendedPaperTradingValidator(config_path)
            self.market_data_tester = LiveMarketDataTester(config_path)
            self.deployment_manager = UltraConservativeLiveDeployment(config_path)
            self.safety_validator = SafetySystemValidator(config_path)

            logger.info("Phase 3 components initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Phase 3 components: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(f"Phase 3 initialization failed: {e}")
            return False

    def start_autonomous_trading(self, mode: str = "paper"):
        """Start autonomous trading mode"""
        if not self.autonomous_orchestrator:
            logger.error("Autonomous orchestrator not initialized")
            return False

        try:
            trading_mode = TradingMode.PAPER if mode == "paper" else TradingMode.LIVE
            # Start autonomous trading in background
            self._run_async_task(self.autonomous_orchestrator.start_autonomous_trading())
            self.autonomous_mode_active = True

            if PYQT_AVAILABLE:
                self.status_changed.emit(f"Autonomous trading started ({mode} mode)")

            logger.info(f"Autonomous trading started in {mode} mode")
            return True

        except Exception as e:
            logger.error(f"Failed to start autonomous trading: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(f"Autonomous trading start failed: {e}")
            return False

    def stop_autonomous_trading(self):
        """Stop autonomous trading mode"""
        if not self.autonomous_orchestrator:
            return False

        try:
            self._run_async_task(self.autonomous_orchestrator.stop_autonomous_trading())
            self.autonomous_mode_active = False

            if PYQT_AVAILABLE:
                self.status_changed.emit("Autonomous trading stopped")

            logger.info("Autonomous trading stopped")
            return True

        except Exception as e:
            logger.error(f"Failed to stop autonomous trading: {e}")
            return False

    def run_paper_trading_validation(self, duration_days: int = 7):
        """Run extended paper trading validation"""
        if not self.paper_trading_validator:
            logger.error("Paper trading validator not initialized")
            return False

        try:
            # Run validation in background
            self._run_async_task(self._run_paper_validation(duration_days))
            return True

        except Exception as e:
            logger.error(f"Failed to start paper trading validation: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(f"Paper trading validation failed: {e}")
            return False

    async def _run_paper_validation(self, duration_days: int):
        """Internal method to run paper trading validation"""
        try:
            success = await self.paper_trading_validator.initialize()
            if success:
                result = await self.paper_trading_validator.run_extended_validation(duration_days)
                self.validation_status['paper_trading'] = result

                if PYQT_AVAILABLE:
                    self.validation_completed.emit({
                        'type': 'paper_trading',
                        'result': result,
                        'success': True
                    })
        except Exception as e:
            logger.error(f"Paper trading validation error: {e}")
            if PYQT_AVAILABLE:
                self.validation_completed.emit({
                    'type': 'paper_trading',
                    'result': None,
                    'success': False,
                    'error': str(e)
                })

    def run_live_market_data_test(self, duration_minutes: int = 30):
        """Run live market data testing"""
        if not self.market_data_tester:
            logger.error("Market data tester not initialized")
            return False

        try:
            self._run_async_task(self._run_market_data_test(duration_minutes))
            return True

        except Exception as e:
            logger.error(f"Failed to start market data test: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(f"Market data test failed: {e}")
            return False

    async def _run_market_data_test(self, duration_minutes: int):
        """Internal method to run market data test"""
        try:
            success = await self.market_data_tester.initialize()
            if success:
                result = await self.market_data_tester.run_comprehensive_test(duration_minutes)
                self.validation_status['market_data'] = result

                if PYQT_AVAILABLE:
                    self.validation_completed.emit({
                        'type': 'market_data',
                        'result': result,
                        'success': True
                    })
        except Exception as e:
            logger.error(f"Market data test error: {e}")
            if PYQT_AVAILABLE:
                self.validation_completed.emit({
                    'type': 'market_data',
                    'result': None,
                    'success': False,
                    'error': str(e)
                })

    def run_safety_system_validation(self):
        """Run comprehensive safety system validation"""
        if not self.safety_validator:
            logger.error("Safety validator not initialized")
            return False

        try:
            self._run_async_task(self._run_safety_validation())
            return True

        except Exception as e:
            logger.error(f"Failed to start safety validation: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(f"Safety validation failed: {e}")
            return False

    async def _run_safety_validation(self):
        """Internal method to run safety validation"""
        try:
            success = await self.safety_validator.initialize()
            if success:
                result = await self.safety_validator.run_comprehensive_safety_validation()
                self.safety_status = result

                if PYQT_AVAILABLE:
                    self.validation_completed.emit({
                        'type': 'safety_validation',
                        'result': result,
                        'success': True
                    })
        except Exception as e:
            logger.error(f"Safety validation error: {e}")
            if PYQT_AVAILABLE:
                self.validation_completed.emit({
                    'type': 'safety_validation',
                    'result': None,
                    'success': False,
                    'error': str(e)
                })

    def check_deployment_readiness(self):
        """Check if system is ready for live deployment"""
        if not self.deployment_manager:
            logger.error("Deployment manager not initialized")
            return False

        try:
            self._run_async_task(self._check_deployment_readiness())
            return True

        except Exception as e:
            logger.error(f"Failed to check deployment readiness: {e}")
            return False

    async def _check_deployment_readiness(self):
        """Internal method to check deployment readiness"""
        try:
            success = await self.deployment_manager.initialize()
            if success:
                checklist_passed = await self.deployment_manager.run_pre_deployment_checklist()
                report = await self.deployment_manager.generate_deployment_report()

                self.deployment_readiness = checklist_passed

                if PYQT_AVAILABLE:
                    self.deployment_status_changed.emit(
                        "READY" if checklist_passed else "NOT_READY"
                    )
                    self.validation_completed.emit({
                        'type': 'deployment_readiness',
                        'result': report,
                        'success': checklist_passed
                    })
        except Exception as e:
            logger.error(f"Deployment readiness check error: {e}")
            if PYQT_AVAILABLE:
                self.deployment_status_changed.emit("ERROR")

    def emergency_stop(self, reason: str = "Manual emergency stop"):
        """Trigger emergency stop of all trading activities"""
        try:
            if self.autonomous_orchestrator:
                self._run_async_task(self.autonomous_orchestrator.emergency_stop(reason))

            self.autonomous_mode_active = False

            if PYQT_AVAILABLE:
                self.safety_alert.emit("CRITICAL", f"Emergency stop triggered: {reason}")
                self.status_changed.emit("EMERGENCY STOP")

            logger.critical(f"Emergency stop triggered: {reason}")
            return True

        except Exception as e:
            logger.error(f"Emergency stop failed: {e}")
            return False

    def get_validation_status(self) -> Dict[str, Any]:
        """Get current validation status"""
        return {
            'paper_trading': self.validation_status.get('paper_trading', None),
            'market_data': self.validation_status.get('market_data', None),
            'safety_validation': self.safety_status,
            'deployment_readiness': self.deployment_readiness,
            'autonomous_active': self.autonomous_mode_active
        }

    def get_safety_metrics(self) -> Dict[str, Any]:
        """Get current safety metrics"""
        if not self.autonomous_orchestrator:
            return {}

        try:
            # Get safety metrics from orchestrator
            return {
                'emergency_stop_active': getattr(self.autonomous_orchestrator, 'emergency_stop_triggered', False),
                'trading_enabled': getattr(self.autonomous_orchestrator, 'trading_enabled', False),
                'cycle_count': getattr(self.autonomous_orchestrator, 'cycle_count', 0),
                'last_decision_time': getattr(self.autonomous_orchestrator, 'last_decision_time', None)
            }
        except Exception as e:
            logger.error(f"Error getting safety metrics: {e}")
            return {}

    def _run_async_task(self, coro):
        """Helper method to run async tasks"""
        def run_in_thread():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(coro)
                loop.close()
            except Exception as e:
                logger.error(f"Async task error: {e}")

        thread = threading.Thread(target=run_in_thread, daemon=True)
        thread.start()
        return thread
    
    def _update_loop(self):
        """Main update loop running in separate thread"""
        while self.is_running:
            try:
                self.update_gui_data()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in GUI update loop: {e}")
                time.sleep(1.0)  # Brief pause before retrying
    
    def update_gui_data(self):
        """Update GUI with latest data"""
        try:
            # Get data from trading system
            if self.trading_system:
                data = self._collect_trading_data()
            else:
                data = self._get_mock_data()
            
            # Update cache
            self.data_cache.update(data)
            
            # Emit signals if PyQt5 is available
            if PYQT_AVAILABLE:
                self.data_updated.emit(data)
            
            # Call registered callbacks
            for callback in self.update_callbacks:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in update callback: {e}")
            
            # Update registered GUI components
            for name, component in self.gui_components.items():
                try:
                    if hasattr(component, 'update_data'):
                        component.update_data(data)
                except Exception as e:
                    logger.error(f"Error updating GUI component {name}: {e}")
        
        except Exception as e:
            logger.error(f"Error updating GUI data: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(str(e))
    
    def _collect_trading_data(self) -> Dict[str, Any]:
        """Collect data from trading system"""
        try:
            data = {
                "timestamp": datetime.now().isoformat(),
                "status": "ACTIVE"
            }
            
            # Get portfolio data
            if hasattr(self.trading_system, 'portfolio'):
                portfolio = self.trading_system.portfolio
                data.update({
                    "balance": getattr(portfolio, 'balance', 0.0),
                    "total_pnl": getattr(portfolio, 'total_pnl', 0.0),
                    "active_positions": len(getattr(portfolio, 'positions', [])),
                })
            
            # Get performance data
            if hasattr(self.trading_system, 'performance_tracker'):
                perf = self.trading_system.performance_tracker
                data.update({
                    "total_trades": getattr(perf, 'total_trades', 0),
                    "win_rate": getattr(perf, 'win_rate', 0.0),
                    "sharpe_ratio": getattr(perf, 'sharpe_ratio', 0.0),
                })
            
            # Get current market data
            if hasattr(self.trading_system, 'current_data'):
                market_data = self.trading_system.current_data
                if market_data:
                    data.update({
                        "current_price": market_data.get('current_price', 0.0),
                        "price_change": market_data.get('price_change_24h', 0.0),
                        "volume": market_data.get('volume', 0.0),
                    })
            
            return data
            
        except Exception as e:
            logger.error(f"Error collecting trading data: {e}")
            return self._get_mock_data()
    
    def _get_mock_data(self) -> Dict[str, Any]:
        """Get mock data for testing"""
        import random
        
        return {
            "timestamp": datetime.now().isoformat(),
            "status": "MOCK",
            "balance": 10000.0 + random.uniform(-1000, 1000),
            "total_pnl": random.uniform(-500, 500),
            "active_positions": random.randint(0, 3),
            "total_trades": random.randint(50, 200),
            "win_rate": random.uniform(0.4, 0.8),
            "sharpe_ratio": random.uniform(-1.0, 2.0),
            "current_price": 50000 + random.uniform(-5000, 5000),
            "price_change": random.uniform(-5.0, 5.0),
            "volume": random.uniform(1000000, 10000000),
            "message": f"Mock update at {datetime.now().strftime('%H:%M:%S')}"
        }
    
    def handle_trade_execution(self, trade_data: Dict[str, Any]):
        """Handle trade execution event"""
        try:
            logger.info(f"Trade executed: {trade_data}")
            
            if PYQT_AVAILABLE:
                self.trade_executed.emit(trade_data)
            
            # Update GUI components
            for name, component in self.gui_components.items():
                if hasattr(component, 'handle_trade'):
                    component.handle_trade(trade_data)
        
        except Exception as e:
            logger.error(f"Error handling trade execution: {e}")
    
    def handle_error(self, error_message: str):
        """Handle error event"""
        logger.error(f"Trading system error: {error_message}")
        
        if PYQT_AVAILABLE:
            self.error_occurred.emit(error_message)
        
        # Notify GUI components
        for name, component in self.gui_components.items():
            if hasattr(component, 'handle_error'):
                component.handle_error(error_message)
    
    def set_status(self, status: str):
        """Set system status"""
        logger.info(f"Status changed: {status}")
        
        if PYQT_AVAILABLE:
            self.status_changed.emit(status)
        
        # Update status in cache
        self.data_cache["status"] = status
        
        # Notify GUI components
        for name, component in self.gui_components.items():
            if hasattr(component, 'set_status'):
                component.set_status(status)
    
    def get_cached_data(self) -> Dict[str, Any]:
        """Get cached data"""
        return self.data_cache.copy()
    
    def force_update(self):
        """Force immediate GUI update"""
        self.update_gui_data()
    
    def _capture_live_terminal_data(self) -> Dict[str, Any]:
        """Capture live terminal data for testing"""
        return {
            'market_data': {
                'symbol': 'BTC/USDT',
                'price': 50000.0,
                'volume': 1000.0
            },
            'ai_analysis': {
                'decision': 'LONG',
                'confidence': 85
            },
            'timeframe_analysis': {
                'overall_trend': 'BULLISH'
            },
            'market_regime': {
                'current_regime': 'TRENDING'
            }
        }

    def _update_data_file(self, data: Dict[str, Any]):
        """Update data file for testing"""
        import json
        try:
            with open('gui_data.json', 'w') as f:
                json.dump(data, f, indent=2)
            logger.info("GUI data file updated successfully")
        except Exception as e:
            logger.error(f"Error updating data file: {e}")

    def cleanup(self):
        """Cleanup resources"""
        self.stop_updates()
        self.gui_components.clear()
        self.update_callbacks.clear()
        logger.info("GUI Integration cleaned up")

# Global instance for easy access
_gui_integration = None

def get_gui_integration(trading_system=None) -> TradingSystemGUIIntegration:
    """Get global GUI integration instance"""
    global _gui_integration
    if _gui_integration is None:
        _gui_integration = TradingSystemGUIIntegration(trading_system)
    return _gui_integration

def cleanup_gui_integration():
    """Cleanup global GUI integration"""
    global _gui_integration
    if _gui_integration:
        _gui_integration.cleanup()
        _gui_integration = None
