# Epinnox v6 GUI Controls Reference - Phase 3

## 🎮 **COMPLETE GUI CONTROLS GUIDE**

This reference provides detailed information about all GUI controls and procedures for live trading with your $50 account.

---

## 📱 **MAIN GUI LAYOUT**

### **Tab Structure** (6 Total Tabs)
1. **Dashboard** - Overview and general information
2. **Autonomous Trading** - Phase 3 autonomous trading controls
3. **Phase 3 Monitoring** - Comprehensive monitoring dashboard
4. **Trading** - Manual trading interface
5. **Portfolio** - Account and position information
6. **Logs** - System logs and activity

---

## 🤖 **TAB 2: AUTONOMOUS TRADING CONTROLS**

### **Primary Controls Section**

#### **Trading Mode Selection**
- **Location**: Top of tab
- **Options**: "Paper Trading" | "Live Trading"
- **⚠️ WARNING**: "Live Trading" uses real money
- **Current Setting**: Shows selected mode

#### **Start/Stop Controls**
- **Start Button**: Green "Start Autonomous Trading" button
  - **Function**: Begins autonomous trading
  - **Requirement**: Trading mode must be selected
  - **Status Change**: <PERSON><PERSON> becomes disabled when active

- **Stop Button**: Red "Stop Autonomous Trading" button
  - **Function**: Stops autonomous trading
  - **Availability**: Only enabled when trading is active
  - **Effect**: Immediately halts all trading activity

#### **Emergency Controls**
- **Emergency Stop**: 🚨 Red "EMERGENCY STOP" button
  - **Function**: Immediate halt of ALL trading
  - **Availability**: Always accessible
  - **Effect**: Stops trading and triggers safety protocols

### **Validation Controls Section**

#### **Paper Trading Validation**
- **Button**: "Run Paper Trading Validation"
- **Duration Setting**: Spinbox (1-30 days, default: 7)
- **Function**: Tests system with simulated trading

#### **Market Data Testing**
- **Button**: "Run Live Market Data Test"
- **Duration Setting**: Spinbox (5-120 minutes, default: 30)
- **Function**: Validates real-time data feeds

#### **Safety System Validation**
- **Button**: "Run Safety System Validation"
- **Function**: Tests all safety mechanisms

#### **Deployment Readiness**
- **Button**: "Check Deployment Readiness"
- **Function**: Comprehensive pre-deployment check

### **Ultra-Conservative Settings Display**
- **Max Total Exposure**: $50 (100% of account)
- **Max Position Size**: $15 (30% of account)
- **Max Daily Loss**: $5 (10% of account)
- **Portfolio Risk**: 1.5% (ultra-conservative)
- **Stop Loss**: 0.8% (tight control)
- **Take Profit**: 1.6% (2:1 risk/reward)

### **Safety Controls Widget** (Integrated)
- **Emergency Stop Button**: Large red button
- **Real-time Safety Monitoring**: Live risk metrics
- **Ultra-Conservative Settings**: Configurable parameters
- **Safety Validation**: Test safety systems

---

## 📊 **TAB 3: PHASE 3 MONITORING DASHBOARD**

### **Validation Results Tab**

#### **Validation Summary**
- **Overall Score**: LCD display (0-100)
- **Validation Indicators**: Status for each validation type
- **Detailed Results Table**: Comprehensive validation data
- **Validation Timeline**: Chronological log of validations

#### **Controls**
- **Refresh Button**: 🔄 "Refresh All" - Updates all data
- **Export Button**: 📊 "Export Report" - Generates comprehensive report

### **Deployment Readiness Tab**

#### **Readiness Overview**
- **Deployment Status**: "READY" | "NOT READY" | "CHECKING"
- **Readiness Progress Bar**: 0-100% completion
- **Safety Checklist**: Pass/fail status
- **Risk Settings Status**: Configuration verification

#### **Pre-Deployment Checklist Table**
- **Requirement**: What needs to be checked
- **Status**: Pass/fail indicator
- **Details**: Additional information

#### **Ultra-Conservative Settings Display**
- **Real-time Values**: Current configuration
- **Color Coding**: Green for safe, red for risky

### **Live Trading Status Tab**

#### **Trading Status Overview**
- **Trading Mode**: "STOPPED" | "ACTIVE"
- **Autonomous Mode**: "INACTIVE" | "ACTIVE"
- **Current Exposure**: Real-time dollar amount
- **Daily P&L**: Running profit/loss total

#### **Performance Metrics**
- **Win Rate**: Percentage of successful trades
- **Total Trades**: Count of completed trades
- **Average Duration**: Typical trade length
- **Sharpe Ratio**: Risk-adjusted performance

#### **Recent Activity Table**
- **Time**: When trade occurred
- **Symbol**: Trading pair
- **Action**: Buy/sell direction
- **Size**: Position size
- **P&L**: Profit/loss result

### **System Health Tab**

#### **Health Overview**
- **System Health Score**: LCD display (0-100)
- **System Uptime**: How long system has been running
- **Component Status**: Individual system health

#### **Connection Status**
- **Market Data**: Connection status
- **LLM System**: AI system status
- **Error Count**: Number of recent errors
- **Memory Usage**: System resource usage

#### **System Logs**
- **Real-time Activity**: Live system events
- **Error Messages**: Any issues or warnings
- **Performance Metrics**: System performance data

---

## 🛡️ **SAFETY CONTROLS WIDGET**

### **Emergency Controls Section**

#### **Emergency Stop Button**
- **Appearance**: Large red button with warning symbols
- **Text**: "🚨 EMERGENCY STOP ALL TRADING 🚨"
- **Function**: Immediate halt of all trading activities
- **Availability**: Always accessible
- **Effect**: Triggers emergency protocols

#### **Status Indicators**
- **Emergency Status**: "NORMAL" | "EMERGENCY STOP ACTIVE"
- **Circuit Breakers**: "ACTIVE" | "TRIGGERED"
- **Color Coding**: Green for normal, red for emergency

### **Real-time Safety Monitoring**

#### **Safety Metrics Display**
- **Total Exposure**: Current dollar exposure
- **Daily P&L**: Running daily profit/loss
- **Risk Level**: "LOW" | "MEDIUM" | "HIGH"
- **Active Positions**: Number of open positions
- **Margin Usage**: Percentage of margin used
- **Last Safety Check**: Time of last safety verification

#### **Safety Score**
- **LCD Display**: 0-100 safety score
- **Color Coding**: Green (safe), orange (caution), red (danger)
- **Real-time Updates**: Continuous monitoring

### **Ultra-Conservative Settings Configuration**

#### **Exposure Controls**
- **Max Total Exposure**: $10-100 range, default $50
- **Max Position Size**: $5-25 range, default $15
- **Max Daily Loss**: $1-15 range, default $5

#### **Risk Controls**
- **Portfolio Risk**: 0.1-3.0% range, default 1.5%
- **Stop Loss**: 0.1-2.0% range, default 0.8%
- **Take Profit**: 0.1-5.0% range, default 1.6%

#### **Position Controls**
- **Max Concurrent Positions**: 1-2 range, default 1
- **Emergency Stop Loss**: $5-20 range, default $8

#### **Apply Settings Button**
- **Function**: Applies all configuration changes
- **Validation**: Checks settings are appropriate
- **Confirmation**: Updates system with new values

### **Safety Validation Section**

#### **Validation Buttons**
- **Run Safety Check**: Comprehensive safety system test
- **Validate Settings**: Verify current configuration
- **Test Emergency Procedures**: Test emergency protocols

#### **Validation Results**
- **Text Display**: Shows validation results
- **Timestamp**: When validation was performed
- **Pass/Fail Status**: Clear indication of results

---

## 🔧 **OPERATIONAL PROCEDURES**

### **Starting Live Trading**
1. **Launch GUI**: `python launch_epinnox.py`
2. **Navigate**: Click "Autonomous Trading" tab
3. **Select Mode**: Choose "Live Trading" from dropdown
4. **Verify Settings**: Check ultra-conservative settings
5. **Start Trading**: Click green "Start Autonomous Trading" button
6. **Monitor**: Watch Phase 3 Monitoring tab for status

### **Monitoring During Trading**
1. **Check Status**: Verify "RUNNING" status in Autonomous tab
2. **Monitor P&L**: Watch real-time profit/loss in Monitoring tab
3. **Check Health**: Ensure system health stays above 80%
4. **Watch Limits**: Verify daily loss doesn't approach $5
5. **Position Count**: Ensure only 1 position maximum

### **Emergency Procedures**
1. **Emergency Stop**: Click red emergency button in any tab
2. **Manual Stop**: Click "Stop Autonomous Trading" button
3. **GUI Restart**: Close and restart application if needed
4. **System Check**: Verify all positions are closed

### **End of Day Procedures**
1. **Stop Trading**: Click "Stop Autonomous Trading"
2. **Review Performance**: Check daily P&L and trade count
3. **Export Report**: Generate daily performance report
4. **System Shutdown**: Close GUI application safely

---

## 📋 **QUICK REFERENCE CHECKLIST**

### **Before Starting Trading**
- [ ] GUI launched successfully
- [ ] Live data flowing (check real-time updates)
- [ ] Trading mode set to "Live Trading"
- [ ] Ultra-conservative settings verified
- [ ] Emergency stop button accessible
- [ ] System health above 85%

### **During Trading**
- [ ] Monitor real-time P&L
- [ ] Check position count (max 1)
- [ ] Watch daily loss limit ($5 max)
- [ ] Verify system health stays high
- [ ] Emergency stop always accessible

### **Emergency Situations**
- [ ] Click emergency stop button
- [ ] Verify all trading stopped
- [ ] Check position status
- [ ] Review system logs
- [ ] Restart system if needed

---

## 🎯 **SUCCESS METRICS**

### **Daily Targets**
- **Profit Target**: $2 (4% of account)
- **Loss Limit**: $5 (10% of account)
- **Trade Limit**: 3 trades maximum
- **Win Rate**: 60%+ target

### **System Performance**
- **Health Score**: 85%+ required
- **Data Quality**: Continuous updates
- **Response Time**: <1 second for controls
- **Uptime**: 95%+ availability

---

**Reference Created**: 2025-07-13 19:30:00  
**GUI Version**: Epinnox v6 with Phase 3 Integration  
**Account Type**: $50 Ultra-Conservative Live Trading  
**Status**: READY FOR DEPLOYMENT ✅
