"""
Configuration utilities for EPINNOX v6
"""

import os
import json
import yaml
from typing import Dict, Any, Optional

def load_config(config_path: str = None) -> Dict[str, Any]:
    """
    Load configuration from file
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    if config_path is None:
        # Try default config files
        config_files = [
            'config/trading_config.yaml',
            'config/config.yaml',
            'config.yaml',
            'config.json'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                config_path = config_file
                break
    
    if config_path is None or not os.path.exists(config_path):
        # Return default configuration
        return get_default_config()
    
    try:
        with open(config_path, 'r') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                return yaml.safe_load(f)
            elif config_path.endswith('.json'):
                return json.load(f)
            else:
                # Try to parse as JSON first, then YAML
                content = f.read()
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    return yaml.safe_load(content)
    except Exception as e:
        print(f"Error loading config from {config_path}: {e}")
        return get_default_config()

def get_default_config() -> Dict[str, Any]:
    """Get default configuration"""
    return {
        'trading': {
            'initial_balance': 10000.0,
            'max_positions': 3,
            'min_confidence': 0.55,
            'slippage': 0.001,
            'commission': 0.001,
            'symbols': ['BTC/USDT', 'ETH/USDT'],
            'timeframe': '1m'
        },
        'risk': {
            'max_portfolio_risk': 0.20,
            'max_position_size': 0.10,
            'max_leverage': 3.0,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.10
        },
        'ml': {
            'use_rl': False,
            'use_lstm': False,
            'model_update_frequency': 100
        },
        'nlp': {
            'use_sentiment': True,
            'sentiment_weight': 0.2
        }
    }

def save_config(config: Dict[str, Any], config_path: str) -> bool:
    """
    Save configuration to file
    
    Args:
        config: Configuration dictionary
        config_path: Path to save configuration
        
    Returns:
        True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        with open(config_path, 'w') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                yaml.dump(config, f, default_flow_style=False)
            else:
                json.dump(config, f, indent=2)
        
        return True
    except Exception as e:
        print(f"Error saving config to {config_path}: {e}")
        return False
