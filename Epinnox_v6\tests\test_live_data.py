#!/usr/bin/env python3
"""
Test script for validating real market data integration
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading.ccxt_trading_engine import CCXTTradingEngine
from data.websocket_client import WebSocketClient
import asyncio
import time

def test_exchange_connection():
    """Test basic exchange connection and data fetching"""
    print("🔗 Testing HTX exchange connection...")
    
    try:
        # Test HTX connection in demo mode
        engine = CCXTTradingEngine('htx', demo_mode=True)
        if engine.initialize_exchange():
            print("✅ HTX exchange connection successful")
            
            # Test fetching live ticker data
            try:
                ticker = engine.exchange.fetch_ticker('BTC/USDT:USDT')
                print(f"✅ Live BTC/USDT price: ${ticker['last']:.2f}")
                print(f"   Bid: ${ticker['bid']:.2f}, Ask: ${ticker['ask']:.2f}")
                print(f"   24h Volume: {ticker['quoteVolume']:,.0f} USDT")
                return True
            except Exception as e:
                print(f"❌ Error fetching ticker: {e}")
                return False
        else:
            print("❌ Failed to connect to HTX exchange")
            return False
    except Exception as e:
        print(f"❌ Exchange connection error: {e}")
        return False

def test_multiple_symbols():
    """Test data fetching for multiple symbols"""
    print("\n📊 Testing multiple symbol data fetching...")
    
    symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
    
    try:
        engine = CCXTTradingEngine('htx', demo_mode=True)
        engine.initialize_exchange()
        
        for symbol in symbols:
            try:
                ticker = engine.exchange.fetch_ticker(symbol)
                print(f"✅ {symbol}: ${ticker['last']:.4f}")
            except Exception as e:
                print(f"❌ {symbol}: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Multiple symbols test error: {e}")
        return False

def test_orderbook_data():
    """Test orderbook data fetching"""
    print("\n📈 Testing orderbook data...")
    
    try:
        engine = CCXTTradingEngine('htx', demo_mode=True)
        engine.initialize_exchange()
        
        orderbook = engine.exchange.fetch_order_book('BTC/USDT:USDT', limit=5)
        print(f"✅ Orderbook fetched - {len(orderbook['bids'])} bids, {len(orderbook['asks'])} asks")
        print(f"   Best bid: ${orderbook['bids'][0][0]:.2f} (size: {orderbook['bids'][0][1]:.4f})")
        print(f"   Best ask: ${orderbook['asks'][0][0]:.2f} (size: {orderbook['asks'][0][1]:.4f})")
        
        # Calculate spread
        spread = orderbook['asks'][0][0] - orderbook['bids'][0][0]
        spread_pct = (spread / orderbook['bids'][0][0]) * 100
        print(f"   Spread: ${spread:.2f} ({spread_pct:.3f}%)")
        
        return True
    except Exception as e:
        print(f"❌ Orderbook test error: {e}")
        return False

def test_ohlcv_data():
    """Test OHLCV data fetching"""
    print("\n📊 Testing OHLCV data...")
    
    try:
        engine = CCXTTradingEngine('htx', demo_mode=True)
        engine.initialize_exchange()
        
        # Fetch recent candles
        ohlcv = engine.exchange.fetch_ohlcv('BTC/USDT:USDT', '1m', limit=5)
        print(f"✅ OHLCV data fetched - {len(ohlcv)} candles")
        
        if ohlcv:
            latest = ohlcv[-1]
            print(f"   Latest candle: O:{latest[1]:.2f} H:{latest[2]:.2f} L:{latest[3]:.2f} C:{latest[4]:.2f}")
            print(f"   Volume: {latest[5]:.4f} BTC")
        
        return True
    except Exception as e:
        print(f"❌ OHLCV test error: {e}")
        return False

def main():
    """Run all live data validation tests"""
    print("🚀 Starting Real Market Data Integration Validation")
    print("=" * 60)
    
    tests = [
        ("Exchange Connection", test_exchange_connection),
        ("Multiple Symbols", test_multiple_symbols),
        ("Orderbook Data", test_orderbook_data),
        ("OHLCV Data", test_ohlcv_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔧 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 LIVE DATA VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All live data integration tests PASSED!")
        return True
    else:
        print("⚠️ Some tests failed. Check configuration and network connectivity.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
