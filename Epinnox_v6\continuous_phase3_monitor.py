#!/usr/bin/env python3
"""
Continuous Phase 3 GUI Integration Monitor
Real-time monitoring during autonomous trading activation
"""

import sys
import os
import time
import json
import threading
from datetime import datetime, timedelta
import psutil

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class Phase3ContinuousMonitor:
    def __init__(self):
        self.monitoring = False
        self.start_time = None
        self.monitoring_duration = 600  # 10 minutes
        self.check_interval = 5  # 5 seconds
        self.log_file = f"logs/phase3_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.metrics_history = []
        
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
    def log_message(self, message, level="INFO"):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        
        # Write to log file
        try:
            with open(self.log_file, 'a') as f:
                f.write(log_entry + "\n")
        except Exception as e:
            print(f"Failed to write to log file: {e}")
    
    def test_phase3_components(self):
        """Test Phase 3 component availability"""
        self.log_message("🔍 Testing Phase 3 component availability...")
        
        components = {}
        
        # Test GUI Integration
        try:
            from gui_integration import TradingSystemGUIIntegration
            gui_integration = TradingSystemGUIIntegration()
            components['gui_integration'] = True
            self.log_message("✅ GUI Integration: Available")
        except Exception as e:
            components['gui_integration'] = False
            self.log_message(f"❌ GUI Integration: Failed - {e}", "ERROR")
        
        # Test Autonomous Trading Tab
        try:
            from gui.autonomous_trading_tab import AutonomousTradingTab
            components['autonomous_tab'] = True
            self.log_message("✅ Autonomous Trading Tab: Available")
        except Exception as e:
            components['autonomous_tab'] = False
            self.log_message(f"❌ Autonomous Trading Tab: Failed - {e}", "ERROR")
        
        # Test Safety Controls Widget
        try:
            from gui.safety_controls_widget import SafetyControlsWidget
            components['safety_controls'] = True
            self.log_message("✅ Safety Controls Widget: Available")
        except Exception as e:
            components['safety_controls'] = False
            self.log_message(f"❌ Safety Controls Widget: Failed - {e}", "ERROR")
        
        # Test Monitoring Dashboard
        try:
            from gui.phase3_monitoring_dashboard import Phase3MonitoringDashboard
            components['monitoring_dashboard'] = True
            self.log_message("✅ Monitoring Dashboard: Available")
        except Exception as e:
            components['monitoring_dashboard'] = False
            self.log_message(f"❌ Monitoring Dashboard: Failed - {e}", "ERROR")
        
        return components
    
    def get_system_metrics(self):
        """Get current system performance metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('.')
            
            # Network (if available)
            try:
                network = psutil.net_io_counters()
                network_sent = network.bytes_sent
                network_recv = network.bytes_recv
            except:
                network_sent = 0
                network_recv = 0
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_free_gb': disk.free / (1024**3),
                'network_sent_mb': network_sent / (1024**2),
                'network_recv_mb': network_recv / (1024**2)
            }
            
            return metrics
            
        except Exception as e:
            self.log_message(f"❌ Failed to get system metrics: {e}", "ERROR")
            return None
    
    def check_gui_process(self):
        """Check if GUI process is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline'] and any('launch_epinnox.py' in cmd for cmd in proc.info['cmdline']):
                        return {
                            'running': True,
                            'pid': proc.info['pid'],
                            'memory_mb': proc.memory_info().rss / (1024**2),
                            'cpu_percent': proc.cpu_percent()
                        }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {'running': False}
            
        except Exception as e:
            self.log_message(f"❌ Failed to check GUI process: {e}", "ERROR")
            return {'running': False, 'error': str(e)}
    
    def monitor_trading_session(self):
        """Monitor active trading session"""
        try:
            # Check if database exists and has active session
            db_path = 'data/epinnox_trading.db'
            if os.path.exists(db_path):
                # Get file modification time to check activity
                mod_time = os.path.getmtime(db_path)
                last_activity = datetime.fromtimestamp(mod_time)
                time_since_activity = datetime.now() - last_activity
                
                return {
                    'database_exists': True,
                    'last_activity': last_activity.isoformat(),
                    'seconds_since_activity': time_since_activity.total_seconds(),
                    'recently_active': time_since_activity.total_seconds() < 30
                }
            else:
                return {'database_exists': False}
                
        except Exception as e:
            self.log_message(f"❌ Failed to monitor trading session: {e}", "ERROR")
            return {'error': str(e)}
    
    def check_log_files(self):
        """Check recent log file activity"""
        try:
            log_dir = 'logs'
            if not os.path.exists(log_dir):
                return {'logs_exist': False}
            
            recent_logs = []
            cutoff_time = datetime.now() - timedelta(minutes=5)
            
            for filename in os.listdir(log_dir):
                if filename.endswith('.log'):
                    filepath = os.path.join(log_dir, filename)
                    mod_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    if mod_time > cutoff_time:
                        recent_logs.append({
                            'filename': filename,
                            'last_modified': mod_time.isoformat(),
                            'size_kb': os.path.getsize(filepath) / 1024
                        })
            
            return {
                'logs_exist': True,
                'recent_logs_count': len(recent_logs),
                'recent_logs': recent_logs[:5]  # Limit to 5 most recent
            }
            
        except Exception as e:
            self.log_message(f"❌ Failed to check log files: {e}", "ERROR")
            return {'error': str(e)}
    
    def perform_comprehensive_check(self):
        """Perform comprehensive system check"""
        self.log_message("🔍 Performing comprehensive system check...")
        
        # Test Phase 3 components
        components = self.test_phase3_components()
        
        # Get system metrics
        metrics = self.get_system_metrics()
        
        # Check GUI process
        gui_status = self.check_gui_process()
        
        # Monitor trading session
        session_status = self.monitor_trading_session()
        
        # Check log files
        log_status = self.check_log_files()
        
        # Compile comprehensive status
        status = {
            'timestamp': datetime.now().isoformat(),
            'components': components,
            'system_metrics': metrics,
            'gui_process': gui_status,
            'trading_session': session_status,
            'log_activity': log_status
        }
        
        # Log summary
        components_ok = sum(components.values())
        total_components = len(components)
        
        self.log_message(f"📊 Component Status: {components_ok}/{total_components} operational")
        
        if gui_status['running']:
            self.log_message(f"✅ GUI Process: Running (PID: {gui_status['pid']}, Memory: {gui_status['memory_mb']:.1f}MB)")
        else:
            self.log_message("❌ GUI Process: Not detected", "WARNING")
        
        if metrics:
            self.log_message(f"📈 System: CPU {metrics['cpu_percent']:.1f}%, Memory {metrics['memory_percent']:.1f}%, Available {metrics['memory_available_gb']:.1f}GB")
        
        if session_status.get('recently_active'):
            self.log_message("✅ Trading Session: Recently active")
        elif session_status.get('database_exists'):
            self.log_message(f"⚠️ Trading Session: Inactive for {session_status['seconds_since_activity']:.0f}s", "WARNING")
        
        # Store metrics history
        self.metrics_history.append(status)
        
        # Keep only last 50 entries
        if len(self.metrics_history) > 50:
            self.metrics_history = self.metrics_history[-50:]
        
        return status
    
    def detect_anomalies(self):
        """Detect system anomalies"""
        if len(self.metrics_history) < 3:
            return []
        
        anomalies = []
        current = self.metrics_history[-1]
        previous = self.metrics_history[-2]
        
        # Check for high CPU usage
        if current['system_metrics'] and current['system_metrics']['cpu_percent'] > 80:
            anomalies.append(f"High CPU usage: {current['system_metrics']['cpu_percent']:.1f}%")
        
        # Check for high memory usage
        if current['system_metrics'] and current['system_metrics']['memory_percent'] > 85:
            anomalies.append(f"High memory usage: {current['system_metrics']['memory_percent']:.1f}%")
        
        # Check for GUI process issues
        if not current['gui_process']['running']:
            anomalies.append("GUI process not detected")
        
        # Check for component failures
        failed_components = [name for name, status in current['components'].items() if not status]
        if failed_components:
            anomalies.append(f"Component failures: {', '.join(failed_components)}")
        
        return anomalies
    
    def start_monitoring(self):
        """Start continuous monitoring"""
        self.monitoring = True
        self.start_time = datetime.now()
        
        self.log_message("🚀 Starting Phase 3 continuous monitoring...")
        self.log_message(f"⏱️ Duration: {self.monitoring_duration} seconds ({self.monitoring_duration/60:.1f} minutes)")
        self.log_message(f"🔄 Check interval: {self.check_interval} seconds")
        self.log_message(f"📝 Log file: {self.log_file}")
        
        check_count = 0
        
        while self.monitoring:
            check_count += 1
            elapsed = (datetime.now() - self.start_time).total_seconds()
            
            # Check if monitoring duration exceeded
            if elapsed >= self.monitoring_duration:
                self.log_message(f"⏰ Monitoring duration completed ({elapsed:.1f}s)")
                break
            
            self.log_message(f"🔍 Check #{check_count} - Elapsed: {elapsed:.1f}s / {self.monitoring_duration}s")
            
            # Perform comprehensive check
            status = self.perform_comprehensive_check()
            
            # Detect anomalies
            anomalies = self.detect_anomalies()
            if anomalies:
                for anomaly in anomalies:
                    self.log_message(f"⚠️ ANOMALY DETECTED: {anomaly}", "WARNING")
            
            # Sleep until next check
            time.sleep(self.check_interval)
        
        self.monitoring = False
        self.log_message("✅ Monitoring completed successfully")
        
        # Generate final report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate final monitoring report"""
        self.log_message("📊 Generating final monitoring report...")
        
        if not self.metrics_history:
            self.log_message("❌ No metrics collected", "ERROR")
            return
        
        # Calculate statistics
        total_checks = len(self.metrics_history)
        successful_checks = sum(1 for m in self.metrics_history if m['gui_process']['running'])
        success_rate = (successful_checks / total_checks) * 100
        
        # CPU and memory statistics
        cpu_values = [m['system_metrics']['cpu_percent'] for m in self.metrics_history if m['system_metrics']]
        memory_values = [m['system_metrics']['memory_percent'] for m in self.metrics_history if m['system_metrics']]
        
        avg_cpu = sum(cpu_values) / len(cpu_values) if cpu_values else 0
        avg_memory = sum(memory_values) / len(memory_values) if memory_values else 0
        max_cpu = max(cpu_values) if cpu_values else 0
        max_memory = max(memory_values) if memory_values else 0
        
        # Component reliability
        component_stats = {}
        for check in self.metrics_history:
            for component, status in check['components'].items():
                if component not in component_stats:
                    component_stats[component] = {'success': 0, 'total': 0}
                component_stats[component]['total'] += 1
                if status:
                    component_stats[component]['success'] += 1
        
        # Generate report
        report = {
            'monitoring_summary': {
                'start_time': self.start_time.isoformat(),
                'end_time': datetime.now().isoformat(),
                'duration_seconds': (datetime.now() - self.start_time).total_seconds(),
                'total_checks': total_checks,
                'successful_checks': successful_checks,
                'success_rate_percent': success_rate
            },
            'system_performance': {
                'average_cpu_percent': avg_cpu,
                'average_memory_percent': avg_memory,
                'max_cpu_percent': max_cpu,
                'max_memory_percent': max_memory
            },
            'component_reliability': {
                component: {
                    'success_rate_percent': (stats['success'] / stats['total']) * 100,
                    'successful_checks': stats['success'],
                    'total_checks': stats['total']
                }
                for component, stats in component_stats.items()
            },
            'overall_assessment': 'STABLE' if success_rate >= 95 else 'UNSTABLE' if success_rate >= 80 else 'CRITICAL'
        }
        
        # Save report
        report_file = f"logs/phase3_monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            self.log_message(f"📄 Report saved: {report_file}")
        except Exception as e:
            self.log_message(f"❌ Failed to save report: {e}", "ERROR")
        
        # Log summary
        self.log_message("="*60)
        self.log_message("📊 FINAL MONITORING REPORT")
        self.log_message("="*60)
        self.log_message(f"Duration: {report['monitoring_summary']['duration_seconds']:.1f} seconds")
        self.log_message(f"Total Checks: {total_checks}")
        self.log_message(f"Success Rate: {success_rate:.1f}%")
        self.log_message(f"Average CPU: {avg_cpu:.1f}%")
        self.log_message(f"Average Memory: {avg_memory:.1f}%")
        self.log_message(f"Overall Assessment: {report['overall_assessment']}")
        self.log_message("="*60)
        
        return report
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        self.log_message("🛑 Monitoring stop requested")

def main():
    """Main monitoring function"""
    monitor = Phase3ContinuousMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        monitor.log_message("⚠️ Monitoring interrupted by user", "WARNING")
        monitor.stop_monitoring()
    except Exception as e:
        monitor.log_message(f"❌ Monitoring failed: {e}", "ERROR")
    
    return monitor

if __name__ == "__main__":
    monitor = main()
