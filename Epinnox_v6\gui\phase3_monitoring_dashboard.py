"""
Phase 3 Monitoring Dashboard - Enhanced GUI Component
Comprehensive monitoring for validation results, deployment readiness, and live trading status
"""

try:
    from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                                QLabel, QPushButton, QTextEdit, QProgressBar,
                                QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox,
                                QTableWidget, QTableWidgetItem, QFrame,
                                QSlider, QComboBox, QLCDNumber, QTabWidget,
                                QScrollArea, QSplitter)
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal
    from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QIcon
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json
import os

logger = logging.getLogger(__name__)

if PYQT_AVAILABLE:
    class Phase3MonitoringDashboard(QWidget):
        """Comprehensive Phase 3 monitoring dashboard"""
        
        # Signals
        refresh_requested = pyqtSignal()
        export_report_requested = pyqtSignal(str)  # report_type
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.gui_integration = None
            self.validation_data = {}
            self.deployment_data = {}
            self.live_trading_data = {}
            self.setup_ui()
            self.setup_timers()
            
            logger.info("Phase 3 Monitoring Dashboard initialized")
        
        def setup_ui(self):
            """Setup the user interface"""
            main_layout = QVBoxLayout(self)
            
            # Title and controls
            header_layout = QHBoxLayout()
            
            title_label = QLabel("Phase 3 Monitoring Dashboard")
            title_label.setFont(QFont("Arial", 16, QFont.Bold))
            header_layout.addWidget(title_label)
            
            header_layout.addStretch()
            
            # Refresh button
            refresh_btn = QPushButton("🔄 Refresh All")
            refresh_btn.clicked.connect(self.refresh_all_data)
            header_layout.addWidget(refresh_btn)
            
            # Export button
            export_btn = QPushButton("📊 Export Report")
            export_btn.clicked.connect(self.export_comprehensive_report)
            header_layout.addWidget(export_btn)
            
            main_layout.addLayout(header_layout)
            
            # Create main content tabs
            tab_widget = QTabWidget()
            main_layout.addWidget(tab_widget)
            
            # Validation Results Tab
            validation_tab = self.create_validation_dashboard()
            tab_widget.addTab(validation_tab, "Validation Results")
            
            # Deployment Readiness Tab
            deployment_tab = self.create_deployment_dashboard()
            tab_widget.addTab(deployment_tab, "Deployment Readiness")
            
            # Live Trading Status Tab
            live_tab = self.create_live_trading_dashboard()
            tab_widget.addTab(live_tab, "Live Trading Status")
            
            # System Health Tab
            health_tab = self.create_system_health_dashboard()
            tab_widget.addTab(health_tab, "System Health")
        
        def create_validation_dashboard(self):
            """Create validation results dashboard"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Validation Summary
            summary_group = QGroupBox("Validation Summary")
            summary_layout = QGridLayout(summary_group)
            
            # Overall validation score
            summary_layout.addWidget(QLabel("Overall Validation Score:"), 0, 0)
            self.overall_score_lcd = QLCDNumber(3)
            self.overall_score_lcd.setStyleSheet("color: green;")
            self.overall_score_lcd.display(0)
            summary_layout.addWidget(self.overall_score_lcd, 0, 1)
            summary_layout.addWidget(QLabel("/100"), 0, 2)
            
            # Validation status indicators
            self.validation_indicators = {}
            validations = [
                ("Paper Trading", "NOT_RUN"),
                ("Market Data Test", "NOT_RUN"),
                ("Safety Validation", "NOT_RUN"),
                ("Deployment Check", "NOT_RUN")
            ]
            
            for i, (validation, status) in enumerate(validations):
                row = 1 + i // 2
                col = (i % 2) * 3
                
                summary_layout.addWidget(QLabel(f"{validation}:"), row, col)
                status_label = QLabel(status)
                status_label.setStyleSheet("color: orange; font-weight: bold;")
                summary_layout.addWidget(status_label, row, col + 1)
                self.validation_indicators[validation] = status_label
            
            layout.addWidget(summary_group)
            
            # Detailed validation results table
            results_group = QGroupBox("Detailed Validation Results")
            results_layout = QVBoxLayout(results_group)
            
            self.validation_results_table = QTableWidget(0, 6)
            self.validation_results_table.setHorizontalHeaderLabels([
                "Validation Type", "Status", "Score", "Duration", "Last Run", "Details"
            ])
            results_layout.addWidget(self.validation_results_table)
            
            layout.addWidget(results_group)
            
            # Validation timeline
            timeline_group = QGroupBox("Validation Timeline")
            timeline_layout = QVBoxLayout(timeline_group)
            
            self.validation_timeline = QTextEdit()
            self.validation_timeline.setMaximumHeight(150)
            self.validation_timeline.setReadOnly(True)
            timeline_layout.addWidget(self.validation_timeline)
            
            layout.addWidget(timeline_group)
            
            return widget
        
        def create_deployment_dashboard(self):
            """Create deployment readiness dashboard"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Deployment readiness overview
            readiness_group = QGroupBox("Deployment Readiness Overview")
            readiness_layout = QGridLayout(readiness_group)
            
            # Overall readiness status
            readiness_layout.addWidget(QLabel("Deployment Status:"), 0, 0)
            self.deployment_status_label = QLabel("NOT READY")
            self.deployment_status_label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
            readiness_layout.addWidget(self.deployment_status_label, 0, 1)
            
            # Readiness percentage
            readiness_layout.addWidget(QLabel("Readiness Score:"), 0, 2)
            self.readiness_progress = QProgressBar()
            self.readiness_progress.setRange(0, 100)
            self.readiness_progress.setValue(0)
            readiness_layout.addWidget(self.readiness_progress, 0, 3)
            
            # Safety checklist status
            readiness_layout.addWidget(QLabel("Safety Checklist:"), 1, 0)
            self.safety_checklist_label = QLabel("0/8 Passed")
            self.safety_checklist_label.setStyleSheet("color: orange; font-weight: bold;")
            readiness_layout.addWidget(self.safety_checklist_label, 1, 1)
            
            # Risk settings status
            readiness_layout.addWidget(QLabel("Risk Settings:"), 1, 2)
            self.risk_settings_label = QLabel("NOT CONFIGURED")
            self.risk_settings_label.setStyleSheet("color: red; font-weight: bold;")
            readiness_layout.addWidget(self.risk_settings_label, 1, 3)
            
            layout.addWidget(readiness_group)
            
            # Deployment checklist
            checklist_group = QGroupBox("Pre-Deployment Checklist")
            checklist_layout = QVBoxLayout(checklist_group)
            
            self.deployment_checklist_table = QTableWidget(0, 3)
            self.deployment_checklist_table.setHorizontalHeaderLabels([
                "Requirement", "Status", "Details"
            ])
            checklist_layout.addWidget(self.deployment_checklist_table)
            
            layout.addWidget(checklist_group)
            
            # Ultra-conservative settings display (optimized for $50 account)
            settings_group = QGroupBox("Ultra-Conservative Settings ($50 Account)")
            settings_layout = QGridLayout(settings_group)

            self.settings_labels = {}
            settings = [
                ("Max Total Exposure", "$50"),
                ("Max Position Size", "$15"),
                ("Max Daily Loss", "$5"),
                ("Portfolio Risk", "1.5%"),
                ("Stop Loss", "0.8%"),
                ("Take Profit", "1.6%")
            ]
            
            for i, (setting, value) in enumerate(settings):
                row = i // 3
                col = (i % 3) * 2
                
                settings_layout.addWidget(QLabel(f"{setting}:"), row, col)
                value_label = QLabel(value)
                value_label.setStyleSheet("font-weight: bold; color: blue;")
                settings_layout.addWidget(value_label, row, col + 1)
                self.settings_labels[setting] = value_label
            
            layout.addWidget(settings_group)
            
            return widget
        
        def create_live_trading_dashboard(self):
            """Create live trading status dashboard"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Trading status overview
            status_group = QGroupBox("Live Trading Status")
            status_layout = QGridLayout(status_group)
            
            # Trading mode
            status_layout.addWidget(QLabel("Trading Mode:"), 0, 0)
            self.trading_mode_label = QLabel("STOPPED")
            self.trading_mode_label.setStyleSheet("color: red; font-weight: bold;")
            status_layout.addWidget(self.trading_mode_label, 0, 1)
            
            # Autonomous status
            status_layout.addWidget(QLabel("Autonomous Mode:"), 0, 2)
            self.autonomous_mode_label = QLabel("INACTIVE")
            self.autonomous_mode_label.setStyleSheet("color: orange; font-weight: bold;")
            status_layout.addWidget(self.autonomous_mode_label, 0, 3)
            
            # Current exposure
            status_layout.addWidget(QLabel("Current Exposure:"), 1, 0)
            self.current_exposure_label = QLabel("$0.00")
            self.current_exposure_label.setStyleSheet("color: green; font-weight: bold;")
            status_layout.addWidget(self.current_exposure_label, 1, 1)
            
            # Daily P&L
            status_layout.addWidget(QLabel("Daily P&L:"), 1, 2)
            self.daily_pnl_label = QLabel("$0.00")
            self.daily_pnl_label.setStyleSheet("color: blue; font-weight: bold;")
            status_layout.addWidget(self.daily_pnl_label, 1, 3)
            
            # Active positions
            status_layout.addWidget(QLabel("Active Positions:"), 2, 0)
            self.active_positions_label = QLabel("0")
            self.active_positions_label.setStyleSheet("color: green; font-weight: bold;")
            status_layout.addWidget(self.active_positions_label, 2, 1)
            
            # Last decision time
            status_layout.addWidget(QLabel("Last Decision:"), 2, 2)
            self.last_decision_label = QLabel("Never")
            self.last_decision_label.setStyleSheet("color: gray; font-weight: bold;")
            status_layout.addWidget(self.last_decision_label, 2, 3)
            
            layout.addWidget(status_group)
            
            # Performance metrics
            performance_group = QGroupBox("Performance Metrics")
            performance_layout = QGridLayout(performance_group)
            
            # Win rate
            performance_layout.addWidget(QLabel("Win Rate:"), 0, 0)
            self.win_rate_label = QLabel("0%")
            performance_layout.addWidget(self.win_rate_label, 0, 1)
            
            # Total trades
            performance_layout.addWidget(QLabel("Total Trades:"), 0, 2)
            self.total_trades_label = QLabel("0")
            performance_layout.addWidget(self.total_trades_label, 0, 3)
            
            # Average trade duration
            performance_layout.addWidget(QLabel("Avg Trade Duration:"), 1, 0)
            self.avg_duration_label = QLabel("N/A")
            performance_layout.addWidget(self.avg_duration_label, 1, 1)
            
            # Sharpe ratio
            performance_layout.addWidget(QLabel("Sharpe Ratio:"), 1, 2)
            self.sharpe_ratio_label = QLabel("N/A")
            performance_layout.addWidget(self.sharpe_ratio_label, 1, 3)
            
            layout.addWidget(performance_group)
            
            # Recent trading activity
            activity_group = QGroupBox("Recent Trading Activity")
            activity_layout = QVBoxLayout(activity_group)
            
            self.trading_activity_table = QTableWidget(0, 5)
            self.trading_activity_table.setHorizontalHeaderLabels([
                "Time", "Symbol", "Action", "Size", "P&L"
            ])
            activity_layout.addWidget(self.trading_activity_table)
            
            layout.addWidget(activity_group)
            
            return widget
        
        def create_system_health_dashboard(self):
            """Create system health monitoring dashboard"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # System health overview
            health_group = QGroupBox("System Health Overview")
            health_layout = QGridLayout(health_group)
            
            # Overall health score
            health_layout.addWidget(QLabel("System Health:"), 0, 0)
            self.health_score_lcd = QLCDNumber(3)
            self.health_score_lcd.setStyleSheet("color: green;")
            self.health_score_lcd.display(100)
            health_layout.addWidget(self.health_score_lcd, 0, 1)
            health_layout.addWidget(QLabel("/100"), 0, 2)
            
            # Uptime
            health_layout.addWidget(QLabel("System Uptime:"), 0, 3)
            self.uptime_label = QLabel("00:00:00")
            self.uptime_label.setStyleSheet("color: blue; font-weight: bold;")
            health_layout.addWidget(self.uptime_label, 0, 4)
            
            # Connection status
            health_layout.addWidget(QLabel("Market Data:"), 1, 0)
            self.market_data_status = QLabel("DISCONNECTED")
            self.market_data_status.setStyleSheet("color: red; font-weight: bold;")
            health_layout.addWidget(self.market_data_status, 1, 1)
            
            # LLM status
            health_layout.addWidget(QLabel("LLM System:"), 1, 2)
            self.llm_status = QLabel("OFFLINE")
            self.llm_status.setStyleSheet("color: red; font-weight: bold;")
            health_layout.addWidget(self.llm_status, 1, 3)
            
            # Error count
            health_layout.addWidget(QLabel("Error Count:"), 2, 0)
            self.error_count_label = QLabel("0")
            self.error_count_label.setStyleSheet("color: green; font-weight: bold;")
            health_layout.addWidget(self.error_count_label, 2, 1)
            
            # Memory usage
            health_layout.addWidget(QLabel("Memory Usage:"), 2, 2)
            self.memory_usage_label = QLabel("N/A")
            self.memory_usage_label.setStyleSheet("color: blue; font-weight: bold;")
            health_layout.addWidget(self.memory_usage_label, 2, 3)
            
            layout.addWidget(health_group)
            
            # System logs
            logs_group = QGroupBox("System Logs")
            logs_layout = QVBoxLayout(logs_group)
            
            self.system_logs = QTextEdit()
            self.system_logs.setReadOnly(True)
            logs_layout.addWidget(self.system_logs)
            
            layout.addWidget(logs_group)
            
            return widget
        
        def setup_timers(self):
            """Setup update timers with thread safety"""
            from PyQt5.QtCore import QMetaObject, Qt
            from PyQt5.QtWidgets import QApplication

            # Main update timer
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_all_displays)
            self.update_timer.setSingleShot(False)

            # Health check timer
            self.health_timer = QTimer()
            self.health_timer.timeout.connect(self.update_system_health)
            self.health_timer.setSingleShot(False)

            # Start timers on main thread
            if QApplication.instance() is not None:
                QMetaObject.invokeMethod(self.update_timer, "start", Qt.QueuedConnection, 3000)
                QMetaObject.invokeMethod(self.health_timer, "start", Qt.QueuedConnection, 5000)
            else:
                self.update_timer.start(3000)  # Fallback
                self.health_timer.start(5000)  # Fallback
        
        def set_gui_integration(self, gui_integration):
            """Set the GUI integration instance"""
            self.gui_integration = gui_integration
            if gui_integration:
                # Connect to validation and status signals
                gui_integration.validation_completed.connect(self.on_validation_completed)
                gui_integration.deployment_status_changed.connect(self.on_deployment_status_changed)
                gui_integration.status_changed.connect(self.on_status_changed)
        
        # Event handlers
        def refresh_all_data(self):
            """Refresh all dashboard data"""
            self.refresh_requested.emit()
            if self.gui_integration:
                # Trigger data refresh from GUI integration
                validation_status = self.gui_integration.get_validation_status()
                self.update_validation_data(validation_status)
                
                safety_metrics = self.gui_integration.get_safety_metrics()
                self.update_live_trading_data(safety_metrics)
            
            self.log_system_event("Dashboard data refreshed")
        
        def export_comprehensive_report(self):
            """Export comprehensive monitoring report"""
            self.export_report_requested.emit("comprehensive")
            
            # Generate report data
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'validation_data': self.validation_data,
                'deployment_data': self.deployment_data,
                'live_trading_data': self.live_trading_data,
                'system_health': self.get_system_health_data()
            }
            
            # Save report
            os.makedirs('logs/reports', exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = f'logs/reports/phase3_monitoring_report_{timestamp}.json'
            
            try:
                with open(report_path, 'w') as f:
                    json.dump(report_data, f, indent=2, default=str)
                self.log_system_event(f"Report exported: {report_path}")
            except Exception as e:
                self.log_system_event(f"Report export failed: {e}")
        
        # Signal handlers
        def on_validation_completed(self, result):
            """Handle validation completion"""
            validation_type = result.get('type', 'unknown')
            success = result.get('success', False)
            
            # Update validation indicators
            if validation_type in self.validation_indicators:
                status = "PASSED" if success else "FAILED"
                color = "green" if success else "red"
                self.validation_indicators[validation_type].setText(status)
                self.validation_indicators[validation_type].setStyleSheet(f"color: {color}; font-weight: bold;")
            
            # Update validation data
            self.validation_data[validation_type] = result
            self.update_validation_results_table()
            
            # Log validation event
            self.log_validation_event(f"{validation_type} validation {status}")
        
        def on_deployment_status_changed(self, status):
            """Handle deployment status changes"""
            self.deployment_status_label.setText(status)
            
            if status == "READY":
                self.deployment_status_label.setStyleSheet("color: green; font-weight: bold; font-size: 14px;")
                self.readiness_progress.setValue(100)
            elif status == "NOT_READY":
                self.deployment_status_label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
                self.readiness_progress.setValue(0)
            else:
                self.deployment_status_label.setStyleSheet("color: orange; font-weight: bold; font-size: 14px;")
                self.readiness_progress.setValue(50)
        
        def on_status_changed(self, status):
            """Handle general status changes"""
            self.log_system_event(f"Status: {status}")
        
        # Update methods
        def update_all_displays(self):
            """Update all dashboard displays"""
            if self.gui_integration:
                # Update validation status
                validation_status = self.gui_integration.get_validation_status()
                self.update_validation_data(validation_status)
                
                # Update safety metrics
                safety_metrics = self.gui_integration.get_safety_metrics()
                self.update_live_trading_data(safety_metrics)
        
        def update_validation_data(self, validation_status):
            """Update validation data displays"""
            self.validation_data.update(validation_status)
            
            # Calculate overall score
            scores = []
            for validation_type, status in validation_status.items():
                if isinstance(status, dict) and 'result' in status:
                    result = status['result']
                    if isinstance(result, dict) and 'overall_safety_score' in result:
                        scores.append(result['overall_safety_score'])
                elif isinstance(status, bool):
                    scores.append(100 if status else 0)
            
            if scores:
                overall_score = sum(scores) / len(scores)
                self.overall_score_lcd.display(int(overall_score))
        
        def update_live_trading_data(self, safety_metrics):
            """Update live trading data displays"""
            self.live_trading_data.update(safety_metrics)
            
            # Update trading status
            if safety_metrics.get('trading_enabled', False):
                self.trading_mode_label.setText("ACTIVE")
                self.trading_mode_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.trading_mode_label.setText("STOPPED")
                self.trading_mode_label.setStyleSheet("color: red; font-weight: bold;")
            
            # Update cycle count
            cycle_count = safety_metrics.get('cycle_count', 0)
            self.total_trades_label.setText(str(cycle_count))
            
            # Update last decision time
            last_decision = safety_metrics.get('last_decision_time', None)
            if last_decision:
                self.last_decision_label.setText(last_decision.strftime("%H:%M:%S"))
            else:
                self.last_decision_label.setText("Never")
        
        def update_validation_results_table(self):
            """Update validation results table"""
            self.validation_results_table.setRowCount(len(self.validation_data))
            
            for row, (validation_type, data) in enumerate(self.validation_data.items()):
                self.validation_results_table.setItem(row, 0, QTableWidgetItem(validation_type))
                
                if isinstance(data, dict):
                    status = "PASSED" if data.get('success', False) else "FAILED"
                    score = str(data.get('result', {}).get('overall_safety_score', 'N/A'))
                    duration = "N/A"  # Could be calculated from timestamps
                    last_run = datetime.now().strftime("%H:%M:%S")
                    details = data.get('error', 'Success') if not data.get('success', True) else 'Success'
                else:
                    status = "PASSED" if data else "FAILED"
                    score = "N/A"
                    duration = "N/A"
                    last_run = "N/A"
                    details = "N/A"
                
                self.validation_results_table.setItem(row, 1, QTableWidgetItem(status))
                self.validation_results_table.setItem(row, 2, QTableWidgetItem(score))
                self.validation_results_table.setItem(row, 3, QTableWidgetItem(duration))
                self.validation_results_table.setItem(row, 4, QTableWidgetItem(last_run))
                self.validation_results_table.setItem(row, 5, QTableWidgetItem(details))
        
        def update_system_health(self):
            """Update system health displays"""
            # This would check actual system health metrics
            # For now, simulate health data
            self.health_score_lcd.display(95)
            self.uptime_label.setText("02:15:30")
            self.market_data_status.setText("CONNECTED")
            self.market_data_status.setStyleSheet("color: green; font-weight: bold;")
            self.error_count_label.setText("0")
        
        def get_system_health_data(self):
            """Get current system health data"""
            return {
                'health_score': 95,
                'uptime': "02:15:30",
                'market_data_connected': True,
                'llm_online': True,
                'error_count': 0,
                'memory_usage': "N/A"
            }
        
        def log_validation_event(self, message):
            """Log validation event"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.validation_timeline.append(f"[{timestamp}] {message}")
        
        def log_system_event(self, message):
            """Log system event"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.system_logs.append(f"[{timestamp}] {message}")

else:
    # Fallback class when PyQt5 is not available
    class Phase3MonitoringDashboard:
        def __init__(self, parent=None):
            logger.warning("PyQt5 not available - Phase3MonitoringDashboard disabled")
