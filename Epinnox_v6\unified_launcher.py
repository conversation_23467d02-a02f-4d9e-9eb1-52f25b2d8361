#!/usr/bin/env python3
"""
Unified Autonomous Trading Launcher for Epinnox v6
Consolidates all deployment scripts into a single production launcher
"""

import asyncio
import argparse
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging for Windows compatibility
log_filename = f'logs/autonomous_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'

# Create file handler with UTF-8 encoding
file_handler = logging.FileHandler(log_filename, encoding='utf-8')
file_handler.setLevel(logging.INFO)

# Create console handler without UTF-8 issues
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Configure root logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

main_logger = logging.getLogger(__name__)

class AutonomousTradingLauncher:
    """
    Unified launcher for autonomous trading system
    Supports live, paper, and simulation modes with consistent configuration
    """
    
    def __init__(self):
        """Initialize the launcher"""
        self.mode = "paper"  # Default to paper trading
        self.config = {}
        self.orchestrator = None
        self.is_running = False
        
        # Safety settings
        self.safety_config = {
            'max_daily_loss_pct': 0.20,    # 20% max daily loss
            'max_position_size_pct': 0.30,  # 30% max position size
            'max_leverage': 3.0,            # 3x max leverage
            'max_concurrent_positions': 2,   # 2 max concurrent positions
            'min_confidence': 0.70,         # 70% min confidence
            'emergency_stop_enabled': True,  # Emergency stop enabled
        }
        
        logger.info("Autonomous trading launcher initialized")
    
    def parse_arguments(self):
        """Parse command line arguments"""
        parser = argparse.ArgumentParser(
            description="Epinnox Autonomous Trading System",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Paper trading (default)
  python unified_launcher.py
  
  # Paper trading with custom balance
  python unified_launcher.py --mode paper --balance 5000
  
  # Live trading (requires valid credentials)
  python unified_launcher.py --mode live --balance 100
  
  # Simulation mode
  python unified_launcher.py --mode simulation --balance 10000
  
  # Custom configuration
  python unified_launcher.py --mode paper --symbols DOGE/USDT:USDT,BTC/USDT:USDT --max-positions 3
            """
        )
        
        parser.add_argument(
            '--mode', 
            choices=['live', 'paper', 'simulation'],
            default='paper',
            help='Trading mode (default: paper)'
        )
        
        parser.add_argument(
            '--balance',
            type=float,
            default=1000.0,
            help='Initial balance (default: 1000.0)'
        )
        
        parser.add_argument(
            '--symbols',
            type=str,
            default='DOGE/USDT:USDT,BTC/USDT:USDT',
            help='Trading symbols (comma-separated, default: DOGE/USDT:USDT,BTC/USDT:USDT)'
        )
        
        parser.add_argument(
            '--max-positions',
            type=int,
            default=2,
            help='Maximum concurrent positions (default: 2)'
        )
        
        parser.add_argument(
            '--max-leverage',
            type=float,
            default=3.0,
            help='Maximum leverage (default: 3.0)'
        )
        
        parser.add_argument(
            '--max-daily-loss',
            type=float,
            default=20.0,
            help='Maximum daily loss percentage (default: 20.0)'
        )
        
        parser.add_argument(
            '--confidence-threshold',
            type=float,
            default=70.0,
            help='Minimum confidence threshold percentage (default: 70.0)'
        )
        
        parser.add_argument(
            '--gui',
            action='store_true',
            help='Launch with GUI interface'
        )
        
        parser.add_argument(
            '--debug',
            action='store_true',
            help='Enable debug logging'
        )
        
        parser.add_argument(
            '--validate-only',
            action='store_true',
            help='Validate configuration and exit'
        )
        
        return parser.parse_args()
    
    def validate_configuration(self, args) -> bool:
        """Validate configuration and credentials"""
        logger.info("[VALIDATION] Validating configuration...")

        # Validate mode-specific requirements
        if args.mode == 'live':
            try:
                import credentials
                if not hasattr(credentials, 'HTX_API_KEY') or not hasattr(credentials, 'HTX_SECRET_KEY'):
                    logger.error("[ERROR] Live trading requires HTX_API_KEY and HTX_SECRET_KEY in credentials.py")
                    return False

                if not credentials.validate_credentials():
                    logger.error("[ERROR] Invalid trading credentials")
                    return False

                # Extra safety checks for live trading
                if args.balance > 1000:
                    logger.warning(f"[WARNING] Large balance for live trading: ${args.balance}")
                    response = input("Continue with live trading? (yes/no): ")
                    if response.lower() != 'yes':
                        return False

                logger.info("[SUCCESS] Live trading credentials validated")

            except ImportError:
                logger.error("[ERROR] credentials.py not found - required for live trading")
                return False

        # Validate safety parameters
        if args.max_daily_loss > 50:
            logger.error("[ERROR] Maximum daily loss cannot exceed 50%")
            return False

        if args.max_leverage > 10:
            logger.error("[ERROR] Maximum leverage cannot exceed 10x")
            return False

        if args.confidence_threshold < 50:
            logger.error("[ERROR] Confidence threshold cannot be below 50%")
            return False

        # Validate symbols
        symbols = [s.strip() for s in args.symbols.split(',')]
        for symbol in symbols:
            if ':' not in symbol or '/' not in symbol:
                logger.error(f"[ERROR] Invalid symbol format: {symbol} (expected format: BASE/QUOTE:SETTLE)")
                return False

        logger.info("[SUCCESS] Configuration validation passed")
        return True
    
    def build_configuration(self, args) -> Dict:
        """Build configuration from arguments"""
        symbols = [s.strip() for s in args.symbols.split(',')]
        
        config = {
            'mode': args.mode,
            'initial_balance': args.balance,
            'active_symbols': symbols,
            'max_open_positions': args.max_positions,
            'max_leverage': args.max_leverage,
            'max_daily_loss_pct': args.max_daily_loss / 100,
            'min_confidence': args.confidence_threshold / 100,
            'gui_enabled': args.gui,
            'debug_mode': args.debug,
            
            # Safety configuration
            'safety': self.safety_config,
            
            # Trading configuration
            'trading': {
                'use_limit_orders_only': True,
                'order_timeout_seconds': 60,
                'max_spread_pct': 0.2,
                'auto_cancel_stale_orders': True,
            },
            
            # AI configuration
            'ai': {
                'llm_decision_interval': 30,  # 30 seconds
                'scalper_gpt_enabled': True,
                'symbol_scanner_enabled': True,
                'quality_thresholds': {
                    'spread_quality': 7.0,
                    'decision_quality': 8.0,
                    'symbol_score': 75.0
                }
            },
            
            # Risk management
            'risk': {
                'position_sizing_method': 'adaptive',
                'volatility_adjustment': True,
                'correlation_limits': True,
                'drawdown_protection': True
            }
        }
        
        return config
    
    async def initialize_system(self) -> bool:
        """Initialize the autonomous trading system"""
        try:
            logger.info(f"[INIT] Initializing autonomous trading system in {self.mode} mode...")

            # Import and initialize orchestrator
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode

            # Convert mode string to enum
            mode_map = {
                'live': TradingMode.LIVE,
                'paper': TradingMode.PAPER,
                'simulation': TradingMode.SIMULATION
            }

            trading_mode = mode_map[self.mode]

            # Initialize orchestrator
            self.orchestrator = AutonomousTradingOrchestrator(
                mode=trading_mode,
                config=self.config
            )

            # Initialize the system
            success = await self.orchestrator.initialize()
            if not success:
                logger.error("[ERROR] Failed to initialize autonomous trading system")
                return False

            logger.info("[SUCCESS] Autonomous trading system initialized successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error initializing system: {e}")
            return False
    
    async def start_trading(self):
        """Start autonomous trading"""
        try:
            logger.info("🎯 Starting autonomous trading...")
            
            # Start the orchestrator
            await self.orchestrator.start()
            self.is_running = True
            
            logger.info("✅ Autonomous trading started successfully")
            
            # Keep running until stopped
            while self.is_running:
                await asyncio.sleep(1)
                
                # Check system health
                if not self.orchestrator.is_healthy():
                    logger.error("🚨 System health check failed - stopping trading")
                    break
            
        except KeyboardInterrupt:
            logger.info("🛑 Received interrupt signal - stopping trading")
        except Exception as e:
            logger.error(f"❌ Error in trading loop: {e}")
        finally:
            await self.stop_trading()
    
    async def stop_trading(self):
        """Stop autonomous trading gracefully"""
        logger.info("🛑 Stopping autonomous trading...")
        
        self.is_running = False
        
        if self.orchestrator:
            await self.orchestrator.stop()
        
        logger.info("✅ Autonomous trading stopped gracefully")
    
    def print_startup_summary(self):
        """Print startup summary"""
        print("\n" + "="*60)
        print("🤖 EPINNOX AUTONOMOUS TRADING SYSTEM v6")
        print("="*60)
        print(f"Mode: {self.mode.upper()}")
        print(f"Initial Balance: ${self.config['initial_balance']:,.2f}")
        print(f"Active Symbols: {', '.join(self.config['active_symbols'])}")
        print(f"Max Positions: {self.config['max_open_positions']}")
        print(f"Max Leverage: {self.config['max_leverage']}x")
        print(f"Max Daily Loss: {self.config['max_daily_loss_pct']:.1%}")
        print(f"Min Confidence: {self.config['min_confidence']:.1%}")
        print(f"GUI Enabled: {self.config['gui_enabled']}")
        print("="*60)
        print("🚨 SAFETY FEATURES ENABLED:")
        print("   ✅ LIMIT ORDERS ONLY (No market orders)")
        print("   ✅ Risk limits enforced")
        print("   ✅ Emergency stop available")
        print("   ✅ Real-time monitoring")
        print("="*60)
        print()

async def main():
    """Main entry point"""
    launcher = AutonomousTradingLauncher()
    
    try:
        # Parse arguments
        args = launcher.parse_arguments()
        
        # Set debug logging if requested
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logger.debug("Debug logging enabled")
        
        # Validate configuration
        if not launcher.validate_configuration(args):
            logger.error("❌ Configuration validation failed")
            sys.exit(1)
        
        # Build configuration
        launcher.config = launcher.build_configuration(args)
        launcher.mode = args.mode
        
        # If validate-only, exit here
        if args.validate_only:
            logger.info("✅ Configuration validation completed successfully")
            sys.exit(0)
        
        # Print startup summary
        launcher.print_startup_summary()
        
        # Initialize system
        if not await launcher.initialize_system():
            logger.error("❌ System initialization failed")
            sys.exit(1)
        
        # Start GUI if requested
        if args.gui:
            logger.info("🖥️ Starting GUI interface...")
            # TODO: Launch GUI interface
        
        # Start trading
        await launcher.start_trading()
        
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    # Run the main function
    asyncio.run(main())
