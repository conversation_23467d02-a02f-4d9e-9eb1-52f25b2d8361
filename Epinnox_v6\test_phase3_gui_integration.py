#!/usr/bin/env python3
"""
Test Phase 3 GUI Integration
Quick test to verify Phase 3 components are properly integrated
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_phase3_imports():
    """Test if Phase 3 components can be imported"""
    print("🔍 Testing Phase 3 component imports...")
    
    try:
        from gui_integration import TradingSystemGUIIntegration
        print("✅ GUI Integration imported successfully")
    except ImportError as e:
        print(f"❌ GUI Integration import failed: {e}")
    
    try:
        from gui.autonomous_trading_tab import AutonomousTradingTab
        print("✅ Autonomous Trading Tab imported successfully")
    except ImportError as e:
        print(f"❌ Autonomous Trading Tab import failed: {e}")
    
    try:
        from gui.safety_controls_widget import SafetyControlsWidget
        print("✅ Safety Controls Widget imported successfully")
    except ImportError as e:
        print(f"❌ Safety Controls Widget import failed: {e}")
    
    try:
        from gui.phase3_monitoring_dashboard import Phase3MonitoringDashboard
        print("✅ Phase 3 Monitoring Dashboard imported successfully")
    except ImportError as e:
        print(f"❌ Phase 3 Monitoring Dashboard import failed: {e}")
    
    try:
        from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
        print("✅ Autonomous Trading Orchestrator imported successfully")
    except ImportError as e:
        print(f"❌ Autonomous Trading Orchestrator import failed: {e}")

def test_phase3_validation_components():
    """Test if Phase 3 validation components can be imported"""
    print("\n🔍 Testing Phase 3 validation component imports...")
    
    try:
        from extended_paper_trading_validator import ExtendedPaperTradingValidator
        print("✅ Extended Paper Trading Validator imported successfully")
    except ImportError as e:
        print(f"❌ Extended Paper Trading Validator import failed: {e}")
    
    try:
        from live_market_data_tester import LiveMarketDataTester
        print("✅ Live Market Data Tester imported successfully")
    except ImportError as e:
        print(f"❌ Live Market Data Tester import failed: {e}")
    
    try:
        from ultra_conservative_live_deployment import UltraConservativeLiveDeployment
        print("✅ Ultra Conservative Live Deployment imported successfully")
    except ImportError as e:
        print(f"❌ Ultra Conservative Live Deployment import failed: {e}")
    
    try:
        from safety_system_final_validation import SafetySystemValidator
        print("✅ Safety System Validator imported successfully")
    except ImportError as e:
        print(f"❌ Safety System Validator import failed: {e}")

def test_gui_integration_initialization():
    """Test GUI integration initialization"""
    print("\n🔍 Testing GUI integration initialization...")
    
    try:
        from gui_integration import TradingSystemGUIIntegration
        
        # Create GUI integration instance
        gui_integration = TradingSystemGUIIntegration()
        print("✅ GUI Integration instance created successfully")
        
        # Test Phase 3 initialization
        success = gui_integration.initialize_phase3_components()
        if success:
            print("✅ Phase 3 components initialized successfully")
        else:
            print("❌ Phase 3 components initialization failed")
        
        # Test validation status
        validation_status = gui_integration.get_validation_status()
        print(f"✅ Validation status retrieved: {len(validation_status)} items")
        
        # Test safety metrics
        safety_metrics = gui_integration.get_safety_metrics()
        print(f"✅ Safety metrics retrieved: {len(safety_metrics)} items")
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")

def test_pyqt_availability():
    """Test PyQt5 availability"""
    print("\n🔍 Testing PyQt5 availability...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt
        print("✅ PyQt5 is available")
        return True
    except ImportError as e:
        print(f"❌ PyQt5 not available: {e}")
        return False

def main():
    """Main test function"""
    print("="*60)
    print("🧪 PHASE 3 GUI INTEGRATION TEST")
    print("="*60)
    
    # Test PyQt5 availability
    pyqt_available = test_pyqt_availability()
    
    # Test Phase 3 imports
    test_phase3_imports()
    
    # Test validation components
    test_phase3_validation_components()
    
    # Test GUI integration initialization
    if pyqt_available:
        test_gui_integration_initialization()
    else:
        print("\n⚠️ Skipping GUI integration test - PyQt5 not available")
    
    print("\n" + "="*60)
    print("🏁 PHASE 3 GUI INTEGRATION TEST COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
