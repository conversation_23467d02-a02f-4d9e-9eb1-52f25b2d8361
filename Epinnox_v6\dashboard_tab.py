"""
Dashboard Tab Module
Main dashboard tab for the Epinnox trading system
"""

try:
    from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                                QLabel, QPushButton, QTextEdit, QProgressBar,
                                QGroupBox, QFrame, QScrollArea, QSplitter)
    from PySide6.QtCore import Qt, QTimer, Signal
    from PySide6.QtGui import QFont, QColor, QPalette
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class DashboardTab(QWidget):
    """
    Main dashboard tab for trading system monitoring
    """

    def __init__(self, parent=None):
        # Handle parent type - if it's not a QWidget, pass None to super()
        if parent and hasattr(parent, '__class__') and not isinstance(parent, QWidget):
            super().__init__(None)
            self.parent_window = parent
        else:
            super().__init__(parent)
            self.parent_window = parent

        self.exchange = None
        self.demo_mode = False

        # Initialize UI
        self.setup_ui()

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_dashboard)
        self.update_timer.start(5000)  # Update every 5 seconds

    def setup_ui(self):
        """Setup the dashboard UI"""
        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("EPINNOX TRADING DASHBOARD")
        title_label.setFont(QFont("Consolas", 16, QFont.Bold))
        title_label.setStyleSheet("color: #00ff44; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Main content area
        content_splitter = QSplitter(Qt.Horizontal)

        # Left panel - Account info and metrics
        left_panel = self.create_left_panel()
        content_splitter.addWidget(left_panel)

        # Right panel - Market data and status
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)

        content_splitter.setSizes([400, 600])
        layout.addWidget(content_splitter)

    def create_left_panel(self):
        """Create left panel with account info"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Account Information
        account_group = QGroupBox("Account Information")
        account_group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        account_layout = QVBoxLayout(account_group)

        self.balance_label = QLabel("Balance: Loading...")
        self.balance_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        account_layout.addWidget(self.balance_label)

        self.equity_label = QLabel("Equity: Loading...")
        self.equity_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        account_layout.addWidget(self.equity_label)

        self.margin_label = QLabel("Margin: Loading...")
        self.margin_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        account_layout.addWidget(self.margin_label)

        layout.addWidget(account_group)

        # Trading Statistics
        stats_group = QGroupBox("Trading Statistics")
        stats_group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        stats_layout = QVBoxLayout(stats_group)

        self.total_trades_label = QLabel("Total Trades: 0")
        self.total_trades_label.setStyleSheet("color: #ddd;")
        stats_layout.addWidget(self.total_trades_label)

        self.win_rate_label = QLabel("Win Rate: 0%")
        self.win_rate_label.setStyleSheet("color: #ddd;")
        stats_layout.addWidget(self.win_rate_label)

        self.total_pnl_label = QLabel("Total PnL: $0.00")
        self.total_pnl_label.setStyleSheet("color: #ddd;")
        stats_layout.addWidget(self.total_pnl_label)

        layout.addWidget(stats_group)

        # System Status
        status_group = QGroupBox("System Status")
        status_group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        status_layout = QVBoxLayout(status_group)

        self.connection_status_label = QLabel("Connection: Checking...")
        self.connection_status_label.setStyleSheet("color: #ffaa00;")
        status_layout.addWidget(self.connection_status_label)

        self.demo_mode_label = QLabel("Mode: Unknown")
        self.demo_mode_label.setStyleSheet("color: #ddd;")
        status_layout.addWidget(self.demo_mode_label)

        layout.addWidget(status_group)

        layout.addStretch()
        return panel

    def create_right_panel(self):
        """Create right panel with market data"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Market Overview
        market_group = QGroupBox("Market Overview")
        market_group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        market_layout = QVBoxLayout(market_group)

        self.market_status_label = QLabel("Market Status: Loading...")
        self.market_status_label.setStyleSheet("color: #ddd;")
        market_layout.addWidget(self.market_status_label)

        layout.addWidget(market_group)

        # Activity Log
        log_group = QGroupBox("Activity Log")
        log_group.setStyleSheet("QGroupBox { color: #ccc; font-weight: bold; }")
        log_layout = QVBoxLayout(log_group)

        self.activity_log = QTextEdit()
        self.activity_log.setStyleSheet("""
            QTextEdit {
                background-color: #1a1a1a;
                color: #00ff44;
                border: 1px solid #333;
                font-family: Consolas;
                font-size: 10px;
            }
        """)
        self.activity_log.setMaximumHeight(200)
        log_layout.addWidget(self.activity_log)

        layout.addWidget(log_group)

        layout.addStretch()
        return panel

    def initialize(self, exchange, demo_mode):
        """Initialize dashboard with exchange and demo mode"""
        self.exchange = exchange
        self.demo_mode = demo_mode

        # Update demo mode display
        mode_text = "DEMO MODE" if demo_mode else "LIVE TRADING"
        mode_color = "#ffaa00" if demo_mode else "#ff4444"
        self.demo_mode_label.setText(f"Mode: {mode_text}")
        self.demo_mode_label.setStyleSheet(f"color: {mode_color}; font-weight: bold;")

        # Log initialization
        self.log_message("Dashboard initialized successfully")

    def update_dashboard(self):
        """Update dashboard data"""
        try:
            if self.exchange:
                # Update connection status
                self.connection_status_label.setText("Connection: Connected")
                self.connection_status_label.setStyleSheet("color: #00ff44;")

                # Update market status
                self.market_status_label.setText("Market Status: Active")

            else:
                self.connection_status_label.setText("Connection: Not Connected")
                self.connection_status_label.setStyleSheet("color: #ff4444;")

        except Exception as e:
            logger.error(f"Error updating dashboard: {e}")
            self.log_message(f"Update error: {e}")

    def log_message(self, message):
        """Add a message to the activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.activity_log.append(formatted_message)

        # Keep log size manageable
        if self.activity_log.document().blockCount() > 100:
            cursor = self.activity_log.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
            cursor.deletePreviousChar()  # Remove the newline

    def refresh_dashboard(self):
        """Refresh dashboard data - called before showing main window"""
        try:
            self.log_message("Dashboard refresh requested")
            self.update_dashboard()
        except Exception as e:
            logger.error(f"Error refreshing dashboard: {e}")
            self.log_message(f"Refresh error: {e}")