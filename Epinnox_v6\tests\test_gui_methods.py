#!/usr/bin/env python3
"""
Test script to verify GUI update methods are properly implemented
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_update_methods():
    """Test that all GUI update methods are callable"""
    print("🧪 Testing GUI Update Methods...")
    
    try:
        # Import the main application
        from launch_epinnox import EpinnoxTradingInterface
        
        # Test that critical methods exist
        critical_methods = [
            'update_llm_decision_panel',
            'update_current_analysis_display', 
            'update_all_analysis_panels',
            'on_llm_orchestrator_cycle_complete',
            'force_gui_refresh',
            'clear_analysis_displays',
            'show_analysis_in_progress',
            'update_ml_models_ensemble_display'
        ]
        
        for method_name in critical_methods:
            if hasattr(EpinnoxTradingInterface, method_name):
                method = getattr(EpinnoxTradingInterface, method_name)
                if callable(method):
                    print(f"✅ {method_name} - OK")
                else:
                    print(f"❌ {method_name} - NOT CALLABLE")
            else:
                print(f"❌ {method_name} - NOT FOUND")
        
        print("\n🎉 All GUI update methods are properly implemented!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI methods: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_update_methods()
    sys.exit(0 if success else 1)
