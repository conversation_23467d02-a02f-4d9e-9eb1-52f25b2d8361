#!/usr/bin/env python3
"""
Configure Ultra-Conservative Settings for $50 Account
Optimized safety parameters for small account live trading
"""

import sys
import os
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def calculate_optimal_settings_for_50_dollars():
    """Calculate optimal ultra-conservative settings for $50 account"""
    
    account_balance = 50.0
    
    # Ultra-conservative settings for $50 account
    settings = {
        # Core Risk Management
        'account_balance': account_balance,
        'max_total_exposure': min(50.0, account_balance),  # Never exceed account balance
        'max_position_size': min(15.0, account_balance * 0.3),  # 30% max per position
        'max_daily_loss': min(5.0, account_balance * 0.1),  # 10% max daily loss
        'max_portfolio_risk': 0.015,  # 1.5% portfolio risk (ultra-conservative)
        
        # Position Management
        'stop_loss_percentage': 0.008,  # 0.8% stop loss (tighter for small account)
        'take_profit_percentage': 0.016,  # 1.6% take profit (2:1 risk/reward)
        'max_concurrent_positions': 1,  # Only 1 position for $50 account
        'emergency_stop_loss': 8.0,  # Emergency stop at $8 loss (16% of account)
        
        # Trading Constraints
        'min_position_size': 5.0,  # Minimum $5 position
        'position_size_increment': 1.0,  # $1 increments
        'max_leverage': 1.0,  # No leverage for safety
        'max_trades_per_day': 3,  # Limit to 3 trades per day
        
        # Safety Buffers
        'safety_buffer': 0.2,  # 20% safety buffer
        'emergency_reserve': 10.0,  # Keep $10 as emergency reserve
        'available_trading_capital': account_balance - 10.0,  # $40 available for trading
        
        # Risk Thresholds
        'daily_loss_threshold': 5.0,  # Stop trading if daily loss >= $5
        'weekly_loss_threshold': 10.0,  # Stop trading if weekly loss >= $10
        'consecutive_loss_limit': 3,  # Stop after 3 consecutive losses
        
        # Performance Targets
        'daily_profit_target': 2.0,  # Target $2 daily profit (4% of account)
        'weekly_profit_target': 8.0,  # Target $8 weekly profit (16% of account)
        'monthly_profit_target': 25.0,  # Target $25 monthly profit (50% of account)
    }
    
    return settings

def validate_settings_for_small_account(settings):
    """Validate settings are appropriate for small account"""
    
    account_balance = settings['account_balance']
    validation_results = []
    
    # Check 1: Max exposure vs account balance
    max_exposure = settings['max_total_exposure']
    if max_exposure <= account_balance:
        validation_results.append(f"✅ Max exposure ${max_exposure} <= ${account_balance}")
    else:
        validation_results.append(f"❌ Max exposure ${max_exposure} > ${account_balance}")
    
    # Check 2: Position size appropriateness
    max_position = settings['max_position_size']
    recommended_max = account_balance * 0.4  # 40% is absolute maximum
    if max_position <= recommended_max:
        validation_results.append(f"✅ Max position ${max_position} <= ${recommended_max:.1f} (safe)")
    else:
        validation_results.append(f"❌ Max position ${max_position} > ${recommended_max:.1f} (too high)")
    
    # Check 3: Daily loss protection
    max_daily_loss = settings['max_daily_loss']
    recommended_daily_loss = account_balance * 0.15  # 15% max recommended
    if max_daily_loss <= recommended_daily_loss:
        validation_results.append(f"✅ Max daily loss ${max_daily_loss} <= ${recommended_daily_loss:.1f} (protected)")
    else:
        validation_results.append(f"❌ Max daily loss ${max_daily_loss} > ${recommended_daily_loss:.1f} (risky)")
    
    # Check 4: Portfolio risk level
    portfolio_risk = settings['max_portfolio_risk'] * 100
    if portfolio_risk <= 2.0:
        validation_results.append(f"✅ Portfolio risk {portfolio_risk}% <= 2% (ultra-conservative)")
    else:
        validation_results.append(f"❌ Portfolio risk {portfolio_risk}% > 2% (too aggressive)")
    
    # Check 5: Stop loss tightness
    stop_loss = settings['stop_loss_percentage'] * 100
    if stop_loss <= 1.0:
        validation_results.append(f"✅ Stop loss {stop_loss}% <= 1% (tight control)")
    else:
        validation_results.append(f"❌ Stop loss {stop_loss}% > 1% (too loose)")
    
    # Check 6: Emergency reserve
    emergency_reserve = settings['emergency_reserve']
    min_reserve = account_balance * 0.15  # 15% minimum reserve
    if emergency_reserve >= min_reserve:
        validation_results.append(f"✅ Emergency reserve ${emergency_reserve} >= ${min_reserve:.1f} (adequate)")
    else:
        validation_results.append(f"❌ Emergency reserve ${emergency_reserve} < ${min_reserve:.1f} (insufficient)")
    
    # Check 7: Position count for small account
    max_positions = settings['max_concurrent_positions']
    if max_positions <= 2:
        validation_results.append(f"✅ Max positions {max_positions} <= 2 (appropriate for small account)")
    else:
        validation_results.append(f"❌ Max positions {max_positions} > 2 (too many for small account)")
    
    return validation_results

def generate_trading_plan_for_50_dollars(settings):
    """Generate specific trading plan for $50 account"""
    
    plan = {
        'account_size': '$50',
        'trading_style': 'Ultra-Conservative Scalping',
        'risk_management': {
            'max_risk_per_trade': f"${settings['max_position_size']} ({(settings['max_position_size']/settings['account_balance'])*100:.1f}% of account)",
            'stop_loss': f"{settings['stop_loss_percentage']*100:.1f}%",
            'take_profit': f"{settings['take_profit_percentage']*100:.1f}%",
            'risk_reward_ratio': f"1:{settings['take_profit_percentage']/settings['stop_loss_percentage']:.1f}",
            'daily_loss_limit': f"${settings['max_daily_loss']} ({(settings['max_daily_loss']/settings['account_balance'])*100:.1f}% of account)"
        },
        'position_management': {
            'max_concurrent_positions': settings['max_concurrent_positions'],
            'position_sizing': f"${settings['min_position_size']} - ${settings['max_position_size']}",
            'leverage': f"{settings['max_leverage']}x (no leverage)",
            'emergency_stop': f"${settings['emergency_stop_loss']} loss"
        },
        'trading_limits': {
            'max_trades_per_day': settings['max_trades_per_day'],
            'trading_capital': f"${settings['available_trading_capital']} (${settings['emergency_reserve']} reserved)",
            'consecutive_loss_limit': settings['consecutive_loss_limit']
        },
        'profit_targets': {
            'daily_target': f"${settings['daily_profit_target']} ({(settings['daily_profit_target']/settings['account_balance'])*100:.1f}% of account)",
            'weekly_target': f"${settings['weekly_profit_target']} ({(settings['weekly_profit_target']/settings['account_balance'])*100:.1f}% of account)",
            'monthly_target': f"${settings['monthly_profit_target']} ({(settings['monthly_profit_target']/settings['account_balance'])*100:.1f}% of account)"
        },
        'safety_measures': {
            'emergency_reserve': f"${settings['emergency_reserve']}",
            'safety_buffer': f"{settings['safety_buffer']*100:.0f}%",
            'daily_loss_threshold': f"${settings['daily_loss_threshold']}",
            'weekly_loss_threshold': f"${settings['weekly_loss_threshold']}"
        }
    }
    
    return plan

def save_configuration(settings, plan):
    """Save configuration to file"""
    
    config = {
        'timestamp': datetime.now().isoformat(),
        'account_balance': settings['account_balance'],
        'settings': settings,
        'trading_plan': plan,
        'validation_status': 'APPROVED_FOR_LIVE_TRADING'
    }
    
    # Create config directory
    os.makedirs('config', exist_ok=True)
    
    # Save main configuration
    config_path = 'config/ultra_conservative_50_dollar_config.json'
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # Save backup with timestamp
    backup_path = f'config/ultra_conservative_50_dollar_config_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(backup_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    return config_path, backup_path

def main():
    """Main configuration function"""
    
    print("="*70)
    print("💰 ULTRA-CONSERVATIVE SETTINGS FOR $50 ACCOUNT")
    print("="*70)
    
    # Calculate optimal settings
    print("🔧 Calculating optimal settings for $50 account...")
    settings = calculate_optimal_settings_for_50_dollars()
    
    # Validate settings
    print("\n🔍 Validating settings for small account safety...")
    validation_results = validate_settings_for_small_account(settings)
    
    for result in validation_results:
        print(f"   {result}")
    
    # Check validation success
    passed_validations = sum(1 for result in validation_results if result.startswith("   ✅"))
    total_validations = len(validation_results)
    validation_rate = (passed_validations / total_validations) * 100
    
    print(f"\n📊 Validation Results: {passed_validations}/{total_validations} ({validation_rate:.1f}%)")
    
    if validation_rate >= 85:
        print("✅ Settings validation PASSED - Safe for $50 account")
        validation_status = "PASSED"
    else:
        print("❌ Settings validation FAILED - Adjustments needed")
        validation_status = "FAILED"
    
    # Generate trading plan
    print("\n📋 Generating trading plan for $50 account...")
    plan = generate_trading_plan_for_50_dollars(settings)
    
    # Display key settings
    print("\n🎯 KEY SETTINGS FOR $50 ACCOUNT:")
    print(f"   💰 Account Balance: ${settings['account_balance']}")
    print(f"   🛡️ Max Position Size: ${settings['max_position_size']} ({(settings['max_position_size']/settings['account_balance'])*100:.1f}% of account)")
    print(f"   📉 Max Daily Loss: ${settings['max_daily_loss']} ({(settings['max_daily_loss']/settings['account_balance'])*100:.1f}% of account)")
    print(f"   🎯 Stop Loss: {settings['stop_loss_percentage']*100:.1f}%")
    print(f"   📈 Take Profit: {settings['take_profit_percentage']*100:.1f}%")
    print(f"   🔢 Max Positions: {settings['max_concurrent_positions']}")
    print(f"   🚨 Emergency Stop: ${settings['emergency_stop_loss']} loss")
    print(f"   💼 Trading Capital: ${settings['available_trading_capital']} (${settings['emergency_reserve']} reserved)")
    
    # Display profit targets
    print("\n🎯 PROFIT TARGETS:")
    print(f"   📅 Daily: ${settings['daily_profit_target']} ({(settings['daily_profit_target']/settings['account_balance'])*100:.1f}% of account)")
    print(f"   📅 Weekly: ${settings['weekly_profit_target']} ({(settings['weekly_profit_target']/settings['account_balance'])*100:.1f}% of account)")
    print(f"   📅 Monthly: ${settings['monthly_profit_target']} ({(settings['monthly_profit_target']/settings['account_balance'])*100:.1f}% of account)")
    
    # Save configuration
    if validation_status == "PASSED":
        print("\n💾 Saving configuration...")
        config_path, backup_path = save_configuration(settings, plan)
        print(f"   ✅ Configuration saved: {config_path}")
        print(f"   ✅ Backup saved: {backup_path}")
    
    print("\n" + "="*70)
    if validation_status == "PASSED":
        print("🎉 $50 ACCOUNT CONFIGURATION COMPLETE - READY FOR LIVE TRADING")
    else:
        print("⚠️ $50 ACCOUNT CONFIGURATION NEEDS REVIEW")
    print("="*70)
    
    return validation_status

if __name__ == "__main__":
    status = main()
    sys.exit(0 if status == "PASSED" else 1)
