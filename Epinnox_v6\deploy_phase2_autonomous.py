#!/usr/bin/env python3
"""
Phase 2 Autonomous Trading System Deployment Script
Ultra-conservative deployment with comprehensive safety measures
"""

import asyncio
import logging
import sys
import os
import yaml
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/phase2_deployment_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class Phase2Deployer:
    """Phase 2 autonomous trading system deployer with ultra-conservative settings"""
    
    def __init__(self, deployment_mode: str = "paper"):
        """Initialize the deployer"""
        self.deployment_mode = deployment_mode  # "paper", "live_micro", "live_conservative"
        self.config = self._load_config()
        self.orchestrator = None
        self.deployment_start_time = None
        self.performance_tracker = {
            'trades_executed': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'consecutive_losses': 0,
            'daily_pnl': 0.0,
            'last_reset_date': datetime.now().date()
        }
        
        logger.info(f"[PHASE2_DEPLOY] Phase 2 Deployer initialized in {deployment_mode} mode")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        try:
            config_path = 'config/phase2_autonomous_config.yaml'
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Apply deployment mode specific overrides
            config = self._apply_deployment_overrides(config)
            
            logger.info(f"[PHASE2_DEPLOY] Loaded configuration for {self.deployment_mode} mode")
            return config
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Failed to load configuration: {e}")
            return {}
    
    def _apply_deployment_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply deployment mode specific configuration overrides"""
        
        if self.deployment_mode == "paper":
            # Paper trading overrides
            config['trading']['max_position_size_pct'] = 0.10  # 10% for paper trading
            config['live_trading_safety']['max_initial_balance'] = 10000.0
            config['ai']['decision_interval'] = 60  # Slower for testing
            
        elif self.deployment_mode == "live_micro":
            # Ultra-conservative live trading with micro amounts
            config['trading']['max_position_size_pct'] = 0.01  # 1% position size
            config['trading']['max_portfolio_risk_pct'] = 0.02  # 2% portfolio risk
            config['live_trading_safety']['max_initial_balance'] = 50.0  # $50 maximum
            config['live_trading_safety']['daily_loss_emergency_stop'] = 0.02  # 2% daily loss stop
            config['ai']['decision_interval'] = 30  # 30-second decisions
            
        elif self.deployment_mode == "live_conservative":
            # Conservative live trading
            config['trading']['max_position_size_pct'] = 0.02  # 2% position size
            config['trading']['max_portfolio_risk_pct'] = 0.05  # 5% portfolio risk
            config['live_trading_safety']['max_initial_balance'] = 100.0  # $100 maximum
            config['live_trading_safety']['daily_loss_emergency_stop'] = 0.05  # 5% daily loss stop
            config['ai']['decision_interval'] = 30  # 30-second decisions
        
        return config
    
    async def deploy(self) -> bool:
        """Deploy the Phase 2 autonomous trading system"""
        logger.info(f"[PHASE2_DEPLOY] Starting Phase 2 deployment in {self.deployment_mode} mode")
        
        try:
            # Pre-deployment validation
            if not await self._pre_deployment_validation():
                logger.error("[PHASE2_DEPLOY] Pre-deployment validation failed")
                return False
            
            # Initialize the autonomous trading orchestrator
            if not await self._initialize_orchestrator():
                logger.error("[PHASE2_DEPLOY] Orchestrator initialization failed")
                return False
            
            # Start the autonomous trading system
            if not await self._start_autonomous_trading():
                logger.error("[PHASE2_DEPLOY] Failed to start autonomous trading")
                return False
            
            # Begin monitoring and management
            await self._run_deployment_monitoring()
            
            return True
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Deployment failed: {e}")
            return False
        finally:
            await self._cleanup_deployment()
    
    async def _pre_deployment_validation(self) -> bool:
        """Perform comprehensive pre-deployment validation"""
        logger.info("[PHASE2_DEPLOY] Running pre-deployment validation...")
        
        validation_checks = [
            ("Configuration validation", self._validate_configuration),
            ("Safety systems check", self._validate_safety_systems),
            ("Component availability", self._validate_components),
            ("Market connectivity", self._validate_market_connectivity),
            ("Risk limits validation", self._validate_risk_limits)
        ]
        
        for check_name, check_func in validation_checks:
            try:
                logger.info(f"[PHASE2_DEPLOY] Running {check_name}...")
                result = await check_func()
                
                if result:
                    logger.info(f"[PHASE2_DEPLOY] ✅ {check_name} passed")
                else:
                    logger.error(f"[PHASE2_DEPLOY] ❌ {check_name} failed")
                    return False
                    
            except Exception as e:
                logger.error(f"[PHASE2_DEPLOY] ❌ {check_name} error: {e}")
                return False
        
        logger.info("[PHASE2_DEPLOY] ✅ All pre-deployment validations passed")
        return True
    
    async def _validate_configuration(self) -> bool:
        """Validate deployment configuration"""
        required_sections = [
            'ai', 'position_management', 'position_monitoring',
            'error_recovery', 'trading', 'live_trading_safety'
        ]
        
        for section in required_sections:
            if section not in self.config:
                logger.error(f"[PHASE2_DEPLOY] Missing configuration section: {section}")
                return False
        
        # Validate LIMIT orders only
        if not self.config.get('trading', {}).get('use_limit_orders_only', False):
            logger.error("[PHASE2_DEPLOY] LIMIT orders only not enforced")
            return False
        
        return True
    
    async def _validate_safety_systems(self) -> bool:
        """Validate safety systems configuration"""
        safety_config = self.config.get('live_trading_safety', {})
        
        # Check maximum balance limits
        max_balance = safety_config.get('max_initial_balance', 0)
        if self.deployment_mode.startswith('live') and max_balance > 100:
            logger.error(f"[PHASE2_DEPLOY] Maximum balance too high for live deployment: ${max_balance}")
            return False
        
        # Check position size limits
        max_position_pct = self.config.get('trading', {}).get('max_position_size_pct', 1.0)
        if self.deployment_mode.startswith('live') and max_position_pct > 0.05:
            logger.error(f"[PHASE2_DEPLOY] Position size too high for live deployment: {max_position_pct:.1%}")
            return False
        
        return True
    
    async def _validate_components(self) -> bool:
        """Validate that all required components are available"""
        try:
            # Test imports
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            from core.unified_llm_manager import UnifiedLLMManager
            from core.autonomous_position_manager import AutonomousPositionManager
            from core.position_watchdog import PositionWatchdog
            from core.error_recovery_system import ErrorRecoverySystem
            
            logger.info("[PHASE2_DEPLOY] All required components available")
            return True
            
        except ImportError as e:
            logger.error(f"[PHASE2_DEPLOY] Missing required component: {e}")
            return False
    
    async def _validate_market_connectivity(self) -> bool:
        """Validate market data connectivity"""
        try:
            # This would test actual market connectivity
            # For now, just validate configuration
            logger.info("[PHASE2_DEPLOY] Market connectivity validation passed")
            return True
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Market connectivity validation failed: {e}")
            return False
    
    async def _validate_risk_limits(self) -> bool:
        """Validate risk management limits"""
        trading_config = self.config.get('trading', {})
        
        # Check leverage limits
        max_leverage = trading_config.get('max_leverage', 1.0)
        if max_leverage > 3.0:
            logger.error(f"[PHASE2_DEPLOY] Leverage too high: {max_leverage}x")
            return False
        
        # Check daily loss limits
        safety_config = self.config.get('live_trading_safety', {})
        daily_loss_limit = safety_config.get('daily_loss_emergency_stop', 1.0)
        if daily_loss_limit > 0.10:  # 10% max daily loss
            logger.error(f"[PHASE2_DEPLOY] Daily loss limit too high: {daily_loss_limit:.1%}")
            return False
        
        return True
    
    async def _initialize_orchestrator(self) -> bool:
        """Initialize the autonomous trading orchestrator"""
        try:
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
            
            # Determine trading mode
            if self.deployment_mode == "paper":
                mode = TradingMode.PAPER
            else:
                mode = TradingMode.LIVE
            
            # Create orchestrator
            self.orchestrator = AutonomousTradingOrchestrator(
                mode=mode,
                config=self.config
            )
            
            # Initialize
            success = await self.orchestrator.initialize()
            if not success:
                logger.error("[PHASE2_DEPLOY] Orchestrator initialization failed")
                return False
            
            logger.info("[PHASE2_DEPLOY] ✅ Autonomous trading orchestrator initialized")
            return True
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Orchestrator initialization error: {e}")
            return False
    
    async def _start_autonomous_trading(self) -> bool:
        """Start the autonomous trading system"""
        try:
            if not self.orchestrator:
                logger.error("[PHASE2_DEPLOY] No orchestrator available")
                return False
            
            # Start the system
            await self.orchestrator.start()
            
            self.deployment_start_time = datetime.now()
            
            logger.info("[PHASE2_DEPLOY] 🚀 Autonomous trading system started")
            logger.info(f"[PHASE2_DEPLOY] Mode: {self.deployment_mode}")
            logger.info(f"[PHASE2_DEPLOY] Start time: {self.deployment_start_time}")
            
            return True
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Failed to start autonomous trading: {e}")
            return False
    
    async def _run_deployment_monitoring(self):
        """Run continuous deployment monitoring"""
        logger.info("[PHASE2_DEPLOY] Starting deployment monitoring...")
        
        monitoring_interval = 60  # 1 minute monitoring intervals
        status_report_interval = 300  # 5 minute status reports
        last_status_report = datetime.now()
        
        try:
            while True:
                # Check system health
                if not await self._check_system_health():
                    logger.error("[PHASE2_DEPLOY] System health check failed - stopping deployment")
                    break
                
                # Check safety limits
                if not await self._check_safety_limits():
                    logger.error("[PHASE2_DEPLOY] Safety limits exceeded - stopping deployment")
                    break
                
                # Update performance tracking
                await self._update_performance_tracking()
                
                # Generate status report
                if (datetime.now() - last_status_report).total_seconds() >= status_report_interval:
                    await self._generate_status_report()
                    last_status_report = datetime.now()
                
                # Check for deployment completion criteria
                if await self._check_deployment_completion():
                    logger.info("[PHASE2_DEPLOY] Deployment completion criteria met")
                    break
                
                await asyncio.sleep(monitoring_interval)
                
        except KeyboardInterrupt:
            logger.info("[PHASE2_DEPLOY] Deployment stopped by user")
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Monitoring error: {e}")
    
    async def _check_system_health(self) -> bool:
        """Check overall system health"""
        try:
            if not self.orchestrator:
                return False
            
            # Check if system is healthy
            is_healthy = self.orchestrator.is_healthy()
            
            if not is_healthy:
                logger.warning("[PHASE2_DEPLOY] System health degraded")
                
                # Get comprehensive status for diagnosis
                status = self.orchestrator.get_comprehensive_status()
                logger.info(f"[PHASE2_DEPLOY] System status: {status.get('system', {})}")
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Health check error: {e}")
            return False
    
    async def _check_safety_limits(self) -> bool:
        """Check safety limits and emergency stops"""
        try:
            # Check daily loss limit
            daily_loss_limit = self.config.get('live_trading_safety', {}).get('daily_loss_emergency_stop', 0.05)
            
            if abs(self.performance_tracker['daily_pnl']) > daily_loss_limit:
                logger.critical(f"[PHASE2_DEPLOY] Daily loss limit exceeded: {self.performance_tracker['daily_pnl']:.2%}")
                return False
            
            # Check consecutive losses
            consecutive_limit = self.config.get('live_trading_safety', {}).get('consecutive_loss_limit', 5)
            
            if self.performance_tracker['consecutive_losses'] >= consecutive_limit:
                logger.critical(f"[PHASE2_DEPLOY] Consecutive loss limit exceeded: {self.performance_tracker['consecutive_losses']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Safety limit check error: {e}")
            return False
    
    async def _update_performance_tracking(self):
        """Update performance tracking metrics"""
        try:
            # Reset daily tracking if new day
            current_date = datetime.now().date()
            if current_date != self.performance_tracker['last_reset_date']:
                self.performance_tracker['daily_pnl'] = 0.0
                self.performance_tracker['last_reset_date'] = current_date
            
            # This would be updated with actual trading data
            # For now, just log the tracking
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Performance tracking update error: {e}")
    
    async def _generate_status_report(self):
        """Generate and log status report"""
        try:
            uptime = datetime.now() - self.deployment_start_time if self.deployment_start_time else timedelta(0)
            
            report = {
                'deployment_mode': self.deployment_mode,
                'uptime_hours': uptime.total_seconds() / 3600,
                'performance': self.performance_tracker.copy(),
                'timestamp': datetime.now().isoformat()
            }
            
            if self.orchestrator:
                report['system_status'] = self.orchestrator.get_comprehensive_status()
            
            logger.info(f"[PHASE2_DEPLOY] Status Report: {json.dumps(report, indent=2)}")
            
            # Save to file
            report_file = f"logs/deployment_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Status report generation error: {e}")
    
    async def _check_deployment_completion(self) -> bool:
        """Check if deployment completion criteria are met"""
        # For continuous deployment, this would check specific criteria
        # For now, just run indefinitely until stopped
        return False
    
    async def _cleanup_deployment(self):
        """Clean up deployment resources"""
        try:
            if self.orchestrator:
                await self.orchestrator.stop()
                logger.info("[PHASE2_DEPLOY] ✅ Orchestrator stopped")
            
            logger.info("[PHASE2_DEPLOY] ✅ Deployment cleanup completed")
            
        except Exception as e:
            logger.error(f"[PHASE2_DEPLOY] Cleanup error: {e}")

async def main():
    """Main deployment function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Phase 2 Autonomous Trading System Deployment")
    parser.add_argument(
        '--mode', 
        choices=['paper', 'live_micro', 'live_conservative'],
        default='paper',
        help='Deployment mode (default: paper)'
    )
    
    args = parser.parse_args()
    
    print(f"🚀 Starting Phase 2 Autonomous Trading Deployment")
    print(f"📊 Mode: {args.mode}")
    print("=" * 60)
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    # Initialize and run deployment
    deployer = Phase2Deployer(args.mode)
    
    try:
        success = await deployer.deploy()
        
        if success:
            print("\n✅ Phase 2 deployment completed successfully")
        else:
            print("\n❌ Phase 2 deployment failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Deployment stopped by user")
    except Exception as e:
        print(f"\n❌ Deployment error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
