"""
Checkbox Functions Module
Handles checkbox-related functionality for the Epinnox trading system
"""

import time
import threading
import logging
from typing import Callable, Optional, Dict, Any

logger = logging.getLogger(__name__)

# Global variables for module state
_exchange = None
_demo_mode = False
_fetch_open_positions = None
_fetch_best_bid = None
_fetch_best_ask = None
_place_limit_order = None
_place_market_order = None
_close_position = None
_fetch_ohlcv = None
_set_leverage = None
_debug = False

# Threading control
_audit_thread = None
_audit_thread_flag = False
_auto_trade_thread = None
_auto_trade_thread_flag = False

def init_checkbox_functions(exchange, demo_mode, fetch_open_positions_func,
                           fetch_best_bid_func, fetch_best_ask_func,
                           place_limit_order_func, place_market_order_func,
                           close_position_func, fetch_ohlcv_func,
                           set_leverage_func, debug_flag):
    """
    Initialize the checkbox functions module with necessary trading functions
    """
    global _exchange, _demo_mode, _fetch_open_positions, _fetch_best_bid, _fetch_best_ask
    global _place_limit_order, _place_market_order, _close_position, _fetch_ohlcv
    global _set_leverage, _debug

    _exchange = exchange
    _demo_mode = demo_mode
    _fetch_open_positions = fetch_open_positions_func
    _fetch_best_bid = fetch_best_bid_func
    _fetch_best_ask = fetch_best_ask_func
    _place_limit_order = place_limit_order_func
    _place_market_order = place_market_order_func
    _close_position = close_position_func
    _fetch_ohlcv = fetch_ohlcv_func
    _set_leverage = set_leverage_func
    _debug = debug_flag

    if _debug:
        logger.info("Checkbox functions module initialized successfully")

def check_position_pnl_ratio(position):
    """
    Calculate the PnL ratio for a position
    Returns the percentage profit/loss
    """
    try:
        if not position:
            return 0.0

        # Extract position data
        entry_price = position.get('entryPrice', 0)
        mark_price = position.get('markPrice', 0) or position.get('currentPrice', 0)
        side = position.get('side', '').lower()

        if not entry_price or not mark_price:
            return 0.0

        # Calculate PnL ratio based on position side
        if side == 'long':
            pnl_ratio = ((mark_price - entry_price) / entry_price) * 100
        elif side == 'short':
            pnl_ratio = ((entry_price - mark_price) / entry_price) * 100
        else:
            return 0.0

        return pnl_ratio

    except Exception as e:
        if _debug:
            logger.error(f"Error calculating PnL ratio: {e}")
        return 0.0

def start_audit_positions_thread(tp_getter: Callable, sl_getter: Callable, status_callback: Callable):
    """
    Start the position auditing thread
    """
    global _audit_thread, _audit_thread_flag

    if _audit_thread and _audit_thread.is_alive():
        if _debug:
            logger.warning("Audit thread already running")
        return

    _audit_thread_flag = True
    _audit_thread = threading.Thread(
        target=_audit_positions_worker,
        args=(tp_getter, sl_getter, status_callback),
        daemon=True
    )
    _audit_thread.start()

    if _debug:
        logger.info("Position audit thread started")

def stop_audit_positions():
    """
    Stop the position auditing thread
    """
    global _audit_thread_flag
    _audit_thread_flag = False

    if _debug:
        logger.info("Position audit thread stopped")

def _audit_positions_worker(tp_getter: Callable, sl_getter: Callable, status_callback: Callable):
    """
    Worker function for position auditing thread
    """
    global _audit_thread_flag

    while _audit_thread_flag:
        try:
            if not _fetch_open_positions:
                time.sleep(5)
                continue

            # Fetch current positions
            positions = _fetch_open_positions(force_refresh=True)

            if not positions:
                time.sleep(5)
                continue

            # Get current TP/SL values
            tp_threshold = tp_getter()
            sl_threshold = sl_getter()

            for position in positions:
                if not _audit_thread_flag:
                    break

                try:
                    # Calculate current PnL ratio
                    pnl_ratio = check_position_pnl_ratio(position)
                    symbol = position.get('symbol', 'Unknown')
                    side = position.get('side', 'Unknown')

                    # Check for take profit condition
                    if pnl_ratio >= tp_threshold:
                        status_callback(f"TP triggered for {symbol} {side}: {pnl_ratio:.2f}% >= {tp_threshold}%")
                        if _close_position and not _demo_mode:
                            try:
                                _close_position(symbol, side)
                                status_callback(f"Position closed: {symbol} {side}")
                            except Exception as e:
                                status_callback(f"Failed to close {symbol} {side}: {e}")

                    # Check for stop loss condition
                    elif pnl_ratio <= -sl_threshold:
                        status_callback(f"SL triggered for {symbol} {side}: {pnl_ratio:.2f}% <= -{sl_threshold}%")
                        if _close_position and not _demo_mode:
                            try:
                                _close_position(symbol, side)
                                status_callback(f"Position closed: {symbol} {side}")
                            except Exception as e:
                                status_callback(f"Failed to close {symbol} {side}: {e}")

                    if _debug:
                        logger.debug(f"Position audit: {symbol} {side} PnL: {pnl_ratio:.2f}%")

                except Exception as e:
                    if _debug:
                        logger.error(f"Error auditing position: {e}")
                    continue

        except Exception as e:
            if _debug:
                logger.error(f"Error in audit positions worker: {e}")
            status_callback(f"Audit error: {e}")

        time.sleep(5)  # Check every 5 seconds

def stop_auto_trade():
    """
    Stop the auto trading functionality
    """
    global _auto_trade_thread_flag
    _auto_trade_thread_flag = False

    if _debug:
        logger.info("Auto trade stopped")