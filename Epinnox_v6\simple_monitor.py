#!/usr/bin/env python3
"""
Simple Phase 3 Monitor for Autonomous Trading
"""

import time
import psutil
import os
from datetime import datetime

def log_message(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_gui_process():
    """Check if GUI process is running"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and any('launch_epinnox.py' in str(cmd) for cmd in proc.info['cmdline']):
                return {
                    'running': True,
                    'pid': proc.info['pid'],
                    'memory_mb': proc.memory_info().rss / (1024**2),
                    'cpu_percent': proc.cpu_percent()
                }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return {'running': False}

def get_system_metrics():
    """Get system metrics"""
    cpu = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    return {
        'cpu_percent': cpu,
        'memory_percent': memory.percent,
        'memory_available_gb': memory.available / (1024**3)
    }

def check_trading_activity():
    """Check trading database activity"""
    db_path = 'data/epinnox_trading.db'
    if os.path.exists(db_path):
        mod_time = os.path.getmtime(db_path)
        last_activity = datetime.fromtimestamp(mod_time)
        seconds_ago = (datetime.now() - last_activity).total_seconds()
        return {
            'exists': True,
            'last_activity': last_activity.strftime("%H:%M:%S"),
            'seconds_ago': seconds_ago,
            'recently_active': seconds_ago < 30
        }
    return {'exists': False}

def main():
    log_message("🚀 Starting Phase 3 GUI Integration Monitor")
    log_message("⏱️ Monitoring for 10 minutes during autonomous trading activation")
    log_message("🔄 Checking every 10 seconds")
    
    start_time = datetime.now()
    check_count = 0
    
    while True:
        check_count += 1
        elapsed = (datetime.now() - start_time).total_seconds()
        
        if elapsed >= 600:  # 10 minutes
            log_message("⏰ 10-minute monitoring period completed")
            break
        
        log_message(f"🔍 Check #{check_count} - Elapsed: {elapsed:.0f}s / 600s")
        
        # Check GUI process
        gui_status = check_gui_process()
        if gui_status['running']:
            log_message(f"✅ GUI Process: Running (PID: {gui_status['pid']}, Memory: {gui_status['memory_mb']:.1f}MB)")
        else:
            log_message("❌ GUI Process: Not detected - CRITICAL ISSUE!")
        
        # Check system metrics
        metrics = get_system_metrics()
        log_message(f"📊 System: CPU {metrics['cpu_percent']:.1f}%, Memory {metrics['memory_percent']:.1f}%, Available {metrics['memory_available_gb']:.1f}GB")
        
        # Check for high resource usage
        if metrics['cpu_percent'] > 80:
            log_message(f"⚠️ WARNING: High CPU usage ({metrics['cpu_percent']:.1f}%)")
        if metrics['memory_percent'] > 85:
            log_message(f"⚠️ WARNING: High memory usage ({metrics['memory_percent']:.1f}%)")
        
        # Check trading activity
        trading = check_trading_activity()
        if trading['exists']:
            if trading['recently_active']:
                log_message(f"✅ Trading Database: Active (last update: {trading['last_activity']})")
            else:
                log_message(f"⚠️ Trading Database: Inactive for {trading['seconds_ago']:.0f}s")
        else:
            log_message("❌ Trading Database: Not found")
        
        # Overall health assessment
        health_score = 0
        if gui_status['running']:
            health_score += 40
        if metrics['cpu_percent'] < 80:
            health_score += 20
        if metrics['memory_percent'] < 85:
            health_score += 20
        if trading.get('recently_active', False):
            health_score += 20
        
        if health_score >= 80:
            log_message(f"💚 System Health: EXCELLENT ({health_score}%)")
        elif health_score >= 60:
            log_message(f"💛 System Health: GOOD ({health_score}%)")
        else:
            log_message(f"❤️ System Health: POOR ({health_score}%) - ATTENTION REQUIRED!")
        
        log_message("-" * 60)
        time.sleep(10)  # Check every 10 seconds
    
    log_message("✅ Monitoring completed successfully")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("⚠️ Monitoring interrupted by user")
    except Exception as e:
        log_message(f"❌ Monitoring error: {e}")
